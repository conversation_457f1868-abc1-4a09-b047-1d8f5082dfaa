import '@nomicfoundation/hardhat-chai-matchers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, IssuerInstance, IssuerStorageInstance } from '@test/common/types'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { expect } from 'chai'
import { ethers } from 'ethers'
import hre from 'hardhat'
import { before } from 'mocha'

describe('initialize()', () => {
  let issuer: IssuerInstance
  let contractManager: ContractManagerInstance
  let issuerStorage: IssuerStorageInstance

  const setupFixture = async () => {
    ;({ issuer, contractManager, issuerStorage } = await contractFixture<IssuerContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('should revert when initialized', async () => {
      const result = issuer.initialize(await contractManager.getAddress(), await issuerStorage.getAddress())
      await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
    })

    it('should revert if address(contractManager) == address(0) or address(issuerStorage) == address(0) ', async () => {
      const IssuerLogicExecuteLib = await (await hre.ethers.getContractFactory('IssuerLogicExecuteLib')).deploy()
      const IssuerLogicCallLib = await (await hre.ethers.getContractFactory('IssuerLogicCallLib')).deploy()
      const newIssuer = await (
        await hre.ethers.getContractFactory('IssuerLogic', {
          libraries: {
            IssuerLogicCallLib: await IssuerLogicCallLib.getAddress(),
            IssuerLogicExecuteLib: await IssuerLogicExecuteLib.getAddress(),
          },
        })
      ).deploy()

      const contractManagerAddr = await contractManager.getAddress()
      const issuerStorageAddr = await issuerStorage.getAddress()

      // case contractManager = 0x0
      await expect(newIssuer.initialize(ethers.ZeroAddress, issuerStorageAddr)).to.be.revertedWith(
        ERR.ISSUER.ISSUER_INVALID_VAL,
      )

      // case accountStorage = 0x0
      await expect(newIssuer.initialize(contractManagerAddr, ethers.ZeroAddress)).to.be.revertedWith(
        ERR.ISSUER.ISSUER_INVALID_VAL,
      )
    })
  })
})
