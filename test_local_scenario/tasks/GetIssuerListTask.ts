import { Network } from '../helpers/constants'
import { BaseTask } from './base-task'

type GetIssuerListArgs = { limit: string; offset: string }

export class GetIssuerListTask extends BaseTask<GetIssuerListArgs> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'getIssuerList')
  }

  protected getDefaultArguments(): Partial<GetIssuerListArgs> {
    return { limit: '100', offset: '0' }
  }
}
