import { Network } from '../helpers/constants'
import { BaseTask } from './base-task'

type GetValidatorListArgs = { limit: string; offset: string }

export class GetValidatorListTask extends BaseTask<GetValidatorListArgs> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'getValidatorList')
  }

  protected getDefaultArguments(): Partial<GetValidatorListArgs> {
    return { limit: '100', offset: '0' }
  }
}
