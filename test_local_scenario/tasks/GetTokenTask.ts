import { Network, networkConfig as ALL_CONFIGS } from '../helpers/constants'
import { GetTokenArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class GetTokenTask extends BaseTask<GetTokenArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'getToken')
  }

  protected getDefaultArguments(): GetTokenArguments {
    const cfg = this.getNetworkConfig()
    type AllConfigs = typeof ALL_CONFIGS
    type FinCfg = AllConfigs[Network.LocalFin]
    type BizCfg = AllConfigs[Network.LocalBiz]

    if (this.network === Network.LocalBiz) {
      const biz = cfg as BizCfg
      return { tokenId: biz.RENEWABLE_ID_1 }
    }
    const fin = cfg as FinCfg
    return { tokenId: fin.TOKEN_ID }
  }
}
