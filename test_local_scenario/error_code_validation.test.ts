import { expect } from 'chai'
import { Network } from './helpers/constants'
import { errorCodeValidationScenario } from './scenarios/24_error_code_validation'
import { accessCtrlErrorValidationScenario } from './scenarios/25_accessctrl_error_validation'
import { providerErrorValidationScenario } from './scenarios/26_provider_error_validation'
import { validatorErrorValidationScenario } from './scenarios/27_validator_error_validation'
import { ibcErrorValidationScenario } from './scenarios/28_ibc_error_validation'
import { transferProxyErrorValidationScenario } from './scenarios/29_transferproxy_error_validation'
import { tokenErrorValidationScenario } from './scenarios/30_token_error_validation'
import { issuerErrorValidationScenario } from './scenarios/31_issuer_error_validation'
import { validateFinancialErrorCodes } from './scenarios/32_financial_error_validation'
import { ERROR } from './scenarios/utils'

describe('Start error code validation scenario', function () {
  this.timeout(0)
  it('error code validation scenario run successfully', async function () {
    const output = await errorCodeValidationScenario(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start AccessCtrl error validation scenario', function () {
  this.timeout(0)
  it('AccessCtrl error validation scenario run successfully', async function () {
    const output = await accessCtrlErrorValidationScenario(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start Provider error validation scenario', function () {
  this.timeout(0)
  it('Provider error validation scenario run successfully', async function () {
    const output = await providerErrorValidationScenario(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start Validator error validation scenario', function () {
  this.timeout(0)
  it('Validator error validation scenario run successfully', async function () {
    const output = await validatorErrorValidationScenario(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start TransferProxy error validation scenario', function () {
  this.timeout(0)
  it('TransferProxy error validation scenario run successfully', async function () {
    const output = await transferProxyErrorValidationScenario(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start Token error validation scenario', function () {
  this.timeout(0)
  it('Token error validation scenario run successfully', async function () {
    const output = await tokenErrorValidationScenario(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start Issuer error validation scenario', function () {
  this.timeout(0)
  it('Issuer error validation scenario run successfully', async function () {
    const output = await issuerErrorValidationScenario(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start IBC error validation scenario', function () {
  this.timeout(0)
  it('IBC error validation scenario run successfully', async function () {
    const output = await ibcErrorValidationScenario(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})

describe('Start Financial & RenewableEnergy error validation scenario', function () {
  this.timeout(0)
  it('Financial & RenewableEnergy error validation scenario run successfully', async function () {
    const output = await validateFinancialErrorCodes(Network.LocalFin)
    expect(output).to.not.include(ERROR)
  })
})