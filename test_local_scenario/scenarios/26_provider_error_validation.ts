import { ERR } from '../../test/common/consts'
import { Network } from '../helpers/constants'
import type { BaseTaskArguments } from '../helpers/task-arguments'
import { AddBizZoneToIssuer } from '../tasks/AddBizZoneToIssuer'
import { BaseTask } from '../tasks/base-task'
import { ModProviderTask } from '../tasks/ModProviderTask'
import { RegisterProvTask } from '../tasks/RegisterProvTask'
import { RegisterTokenTask } from '../tasks/RegisterTokenTask'
import { TestResultManager } from './error-validation-utils'

/**
 * Provider系エラーコードの検証シナリオ
 * 実際のSolidityコードの使用場所に基づいたテストを実行
 */
export async function providerErrorValidationScenario(network: Network): Promise<string> {
  const resultManager = new TestResultManager('Provider Error Validation Scenario')

  console.info('=== Provider Error Validation Scenario Started ===')

  const testAccountId = 'PROVIDER_TEST_ACC'

  try {
    // 1. プロバイダID存在エラーの検証
    console.info('1. Testing provider ID exist error')
    await testProviderIdExistError(network, testAccountId, resultManager)

    // 2. プロバイダID不存在エラーの検証
    console.info('2. Testing provider ID not exist error')
    await testProviderIdNotExistError(network, testAccountId, resultManager)

    // 3. プロバイダ無効値エラーの検証
    console.info('3. Testing provider invalid value error')
    await testProviderInvalidValueError(network, testAccountId, resultManager)

    // 4. プロバイダIDでないエラーの検証
    console.info('4. Testing not provider ID error')
    await testNotProviderIdError(network, testAccountId, resultManager)

    // 5. プロバイダロールなしエラーの検証
    console.info('5. Testing provider not role error')
    await testProviderNotRoleError(network, testAccountId, resultManager)

    // 6. ゾーン不存在エラーの検証
    console.info('6. Testing zone not exist error')
    await testZoneNotExistError(network, testAccountId, resultManager)

    // 7. プロバイダコントラクトでないエラーの検証
    console.info('7. Testing not provider contract error')
    await testNotProviderContractError(network, testAccountId, resultManager)

    // 8. プロバイダ管理者権限なしエラーの検証
    console.info('8. Testing provider not admin role error')
    await testProviderNotAdminRoleError(network, testAccountId, resultManager)

    // 結果サマリー出力
    resultManager.printSummary()
    return resultManager.toLegacyResult()
  } catch (error) {
    console.error('Error in Provider error validation scenario:', String(error))
    resultManager.recordFail('Scenario Execution', `Unexpected error: ${String(error)}`)
    return resultManager.toLegacyResult()
  }
}

/**
 * プロバイダID存在エラーのテスト
 * GE1001_PROV_ID_EXIST: "GE1001:exist providerID"
 *
 * 使用場所: Provider.sol.addProvider() Line 124
 * 条件: require(_providerId == 0x00, Error.GE1001_PROV_ID_EXIST)
 * 誘発方法: 既に存在するプロバイダIDで新規登録を試行
 */
async function testProviderIdExistError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Provider ID Exist Error'
  const expectedErrorCode = ERR.PROV.PROV_ID_EXIST

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: Provider.sol.addProvider() Line 124 - require(_providerId == 0x00, ...)')
  try {
    // 登録済みのProvider IDで再登録（force指定でwrapperのガードを回避）
    const dupRes = await new RegisterProvTask(network).execute({ flag: '10', force: 'true' })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, dupRes)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * プロバイダID不存在エラーのテスト
 * GE0101_PROV_ID_NOT_EXIST: "GE0101:not exist"
 *
 * 使用場所:
 * 1. Provider.sol.modProvider(): require(_providerId != 0x00, ...)
 * 2. Provider.sol.addBizZoneToIssuer()
 * 3. Token.sol.registerToken(): require(success, ...)
 *
 * 誘発方法: 存在しないプロバイダIDで操作を実行
 */
async function testProviderIdNotExistError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Provider ID Not Exist Error'
  const expectedErrorCode = ERR.PROV.PROV_ID_NOT_EXIST

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: Provider.modProvider(), Token.registerToken()')

  try {
    // 存在しないprovIdでの名称変更（modProvider）を実行してエラーを誘発
    const modRes = await new ModProviderTask(network).execute({ provId: 'UNREGISTERED_PROV', provName: 'x' })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, modRes)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * プロバイダ無効値エラーのテスト
 * RV0003_PROV_INVALID_VAL: "RV0003:provider invalid value"
 *
 * 使用場所:
 * 1. Provider.sol.addProvider(): require(providerId != 0x00, ...)
 * 2. Provider.sol.modProvider(): require(providerEoa != address(0), ...)
 *
 * 誘発方法: 無効なプロバイダ値（0x00、address(0)等）で操作を実行
 */
async function testProviderInvalidValueError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Provider Invalid Value Error'
  const expectedErrorCode = ERR.PROV.PROV_INVALID_VAL

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: Provider.addProvider(), modProvider()')

  try {
    // provId空（0x00）でのmodProviderにより invalid value を誘発
    const invalidProviderResult = await new ModProviderTask(network).execute({ provId: '', provName: 'x' })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, invalidProviderResult)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * プロバイダIDでないエラーのテスト
 * GE2002_NOT_PROVIDER_ID: "GE2002:not provider id"
 *
 * 使用場所: Provider.sol.registerToken()
 * 条件: require(providerId == _providerId, Error.GE2002_NOT_PROVIDER_ID)
 * 誘発方法: 一致しないプロバイダIDでトークン登録を実行
 */
async function testNotProviderIdError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Not Provider ID Error'
  const expectedErrorCode = ERR.PROV.NOT_PROVIDER_ID

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: Provider.registerToken() - require(providerId == _providerId, ...)')

  try {
    // Provider.addTokenでproviderIdと内部の_providerId不一致を起こす
    const mismatchProviderResult = await new RegisterTokenTask(network).execute({
      provId: 'MISMATCHED_PROVIDER_ID',
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, mismatchProviderResult)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * プロバイダロールなしエラーのテスト
 * GA0004_PROV_NOT_ROLE: "GA0004:not prov"
 *
 * 使用場所:
 * 1. Provider.sol.registerToken(): require(has, Error.GA0004_PROV_NOT_ROLE)
 * 2. Provider.sol.modifyToken(): 同様のパターン
 * 3. Token.sol.registerToken(): ロール検証
 *
 * 誘発方法: プロバイダロールが必要な操作を非プロバイダで実行
 */
async function testProviderNotRoleError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Provider Not Role Error'
  const expectedErrorCode = ERR.PROV.PROV_NOT_ROLE

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: Provider.registerToken(), Token.registerToken()')

  try {
    // 正しいproviderIdで、ロール未付与の鍵で署名して GA0004 を誘発
    const provRoleResult = await new RegisterTokenTask(network).execute({
      provKey: '47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a',
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, provRoleResult)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * ゾーン不存在エラーのテスト
 * GE0103_ZONE_NOT_EXIST: "GE0103:zone not exist"
 *
 * 使用場所:
 * 1. Provider.sol.addBizZoneToIssuer(): require(has, Error.GE0103_ZONE_NOT_EXIST)
 * 2. Provider.sol.deleteBizZoneToIssuer(): 同様のパターン
 *
 * 誘発方法: 存在しないゾーンIDでBizZone操作を実行
 */
async function testZoneNotExistError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Zone Not Exist Error'
  const expectedErrorCode = ERR.PROV.ZONE_NOT_EXIST

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: Provider.addBizZoneToIssuer(), deleteBizZoneToIssuer()')

  try {
    // 存在しないZone IDを指定して Issuer 経由で Provider.addBizZoneToIssuer を呼ぶ
    const zoneNotExistResult = await new AddBizZoneToIssuer(network).execute({
      issuerId: '2222',
      zoneId: '9999',
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, zoneNotExistResult)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * プロバイダコントラクトでないエラーのテスト
 * GA0005_NOT_PROVIDER_CONTRACT: "GA0005:not provider contract"
 *
 * 使用場所: Token.sol の複数の関数
 * 条件: msg.sender == address(_contractManager.provider()) のチェック失敗
 * 誘発方法: プロバイダコントラクト以外からプロバイダ限定操作を実行
 */
async function testNotProviderContractError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Not Provider Contract Error'
  const expectedErrorCode = ERR.PROV.NOT_PROVIDER_CONTRACT

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: Token.sol - msg.sender == address(_contractManager.provider()) checks')

  try {
    // Directly call Token.getToken() from an EOA to induce GA0005
    const out = await runTask(network, 'tokenGetDirect', {})
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, out)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

// Minimal helper to run a hardhat task by name (no any)

async function runTask(network: Network, taskName: string, args: Record<string, string>): Promise<string> {
  class QuickTask extends BaseTask<BaseTaskArguments> {
    filePath = __filename
    constructor(network: Network, name: string) {
      super(network, name)
    }
    protected getDefaultArguments(): Partial<BaseTaskArguments> {
      return {}
    }
  }
  return await new QuickTask(network, taskName).execute(args)
}

/**
 * プロバイダ管理者権限なしエラーのテスト
 * GA0006_PROV_NOT_ADMIN_ROLE: "GA0006:provider not admin"
 *
 * 使用場所: Provider.sol.setProviderAll()
 * 条件: require(has, Error.GA0006_PROV_NOT_ADMIN_ROLE)
 * 誘発方法: 管理者権限が必要なプロバイダ操作を非管理者で実行
 */
async function testProviderNotAdminRoleError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Provider Not Admin Role Error'
  const expectedErrorCode = ERR.PROV.PROV_NOT_ADMIN_ROLE

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: Provider.setProviderAll() - require(has, ...)')

  try {
    // Admin権限が必要な操作に非Admin鍵で署名
    const provAdminResult = await new ModProviderTask(network).execute({
      provId: '1111',
      provName: 'x',
      signerKey: '59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d',
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, provAdminResult)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}
