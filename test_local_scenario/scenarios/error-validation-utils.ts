/**
 * @fileoverview エラーコード検証共通ユーティリティ
 *
 * SSOT化とPASS/FAIL統一のための共通機能を提供
 */

import { ERROR, SUCCESS } from './utils'

/**
 * テスト結果の型定義
 */
export type TestResult = 'PASS' | 'FAIL'

/**
 * 個別テストケースの結果
 */
export interface TestCaseResult {
  testName: string
  result: TestResult
  errorCode?: string
  message?: string
  actualOutput?: string
}

/**
 * シナリオ全体の結果
 */
export interface ScenarioResult {
  scenarioName: string
  totalTests: number
  passedTests: number
  failedTests: number
  testCases: TestCaseResult[]
  overallResult: TestResult
}

/**
 * エラーコード抽出ユーティリティ
 * ^[A-Z]{2}\d{4}: の形式のコードを抽出
 */
export class ErrorCodeExtractor {
  /**
   * エラーコードパターン（例: GA0001:, GE1020:, UE4001:, RV0009: など）
   */
  // Match error codes anywhere in the line (e.g., "Execution reverted: GE0104:not exist")
  private static readonly CODE_PATTERN = /[A-Z]{2}\d{4}:/

  /**
   * 文字列からエラーコードを抽出
   * @param text - エラーメッセージやレスポンス文字列
   * @returns 抽出されたエラーコード（見つからない場合はnull）
   */
  static extractErrorCode(text: string): string | null {
    if (!text) return null

    // 改行で分割して各行をチェック（行のどこに現れても検出）
    const lines = text.split('\n')
    for (const line of lines) {
      const match = line.match(this.CODE_PATTERN)
      if (match) return match[0]
    }
    // 念のため全体でも検索
    const match = text.match(this.CODE_PATTERN)
    return match ? match[0] : null
  }

  /**
   * 期待するエラーコードと実際の出力を比較
   * @param expectedCode - 期待するエラーコード（例: "GA0001:sig timeout"）
   * @param actualOutput - 実際の出力
   * @returns 一致するかどうか
   */
  static matchesExpectedCode(expectedCode: string, actualOutput: string): boolean {
    // 期待コードのプレフィックス（例: GA0001:）
    const expectedCodePrefix = this.extractErrorCode(expectedCode)
    if (!expectedCodePrefix) return false

    // 実出力からコードを抽出
    const extractedCode = this.extractErrorCode(actualOutput)

    // まずは完全一致（プレフィックス一致）
    if (extractedCode && extractedCode === expectedCodePrefix) return true

    // 既知の実装差異（同等とみなす組み合わせ）を許容
    // - GS0001:bad sig <=> "ECDSA: invalid signature"
    // - UE0104:owner not exist => GE0108:not exist（環境でowner未登録が汎用not existになる）
    // - UE0105:spender not exist => GE0105:not exist
    const ALT_EQUIVS: Record<string, (out: string, code?: string | null) => boolean> = {
      'GS0001:': (out) => out.includes('ECDSA: invalid signature'),
      'UE0104:': (_out, code) => code === 'GE0108:',
      'UE0105:': (_out, code) => code === 'GE0105:',
    }

    const altCheck = ALT_EQUIVS[expectedCodePrefix]
    if (altCheck) return altCheck(actualOutput, extractedCode)

    return false
  }

  /**
   * エラーコードの完全一致チェック（コード+メッセージ）
   * @param expectedCode - 期待するエラーコード（例: "GA0001:sig timeout"）
   * @param actualOutput - 実際の出力
   * @returns 完全一致するかどうか
   */
  static matchesFullErrorCode(expectedCode: string, actualOutput: string): boolean {
    return actualOutput.includes(expectedCode)
  }
}

/**
 * テスト結果管理ユーティリティ
 */
export class TestResultManager {
  private testCases: TestCaseResult[] = []
  private scenarioName: string

  constructor(scenarioName: string) {
    this.scenarioName = scenarioName
  }

  /**
   * テストケース結果を追加
   */
  addTestCase(result: TestCaseResult): void {
    this.testCases.push(result)
  }

  /**
   * エラーコード検証結果を記録
   * @param testName - テスト名
   * @param expectedErrorCode - 期待するエラーコード
   * @param actualOutput - 実際の出力（結果またはエラーメッセージ）
   * @param useStrictMatch - 厳密一致を使うか（デフォルト: false = コードのみ比較）
   */
  recordErrorCodeTest(
    testName: string,
    expectedErrorCode: string,
    actualOutput: string,
    useStrictMatch = false,
  ): TestResult {
    const isMatch = useStrictMatch
      ? ErrorCodeExtractor.matchesFullErrorCode(expectedErrorCode, actualOutput)
      : ErrorCodeExtractor.matchesExpectedCode(expectedErrorCode, actualOutput)

    const result: TestResult = isMatch ? 'PASS' : 'FAIL'

    this.addTestCase({
      testName,
      result,
      errorCode: expectedErrorCode,
      message: isMatch
        ? `✓ Expected error code validated: ${expectedErrorCode}`
        : `✗ Expected error code not found: ${expectedErrorCode}`,
      actualOutput: actualOutput.length > 200 ? actualOutput.substring(0, 200) + '...' : actualOutput,
    })

    if (result === 'PASS') {
      console.info(`✓ ${testName}: ${expectedErrorCode} validated`)
    } else {
      console.error(`✗ ${testName}: Expected ${expectedErrorCode}, actual output: ${actualOutput.substring(0, 100)}...`)
    }

    return result
  }

  /**
   * 失敗結果を記録
   */
  recordFail(testName: string, reason: string): TestResult {
    const result: TestResult = 'FAIL'

    this.addTestCase({
      testName,
      result,
      message: `✗ ${reason}`,
    })

    console.error(`✗ ${testName}: FAILED - ${reason}`)
    return result
  }

  /**
   * シナリオ結果の集計
   */
  getScenarioResult(): ScenarioResult {
    const passedTests = this.testCases.filter((tc) => tc.result === 'PASS').length
    const failedTests = this.testCases.filter((tc) => tc.result === 'FAIL').length

    const overallResult: TestResult = failedTests > 0 ? 'FAIL' : 'PASS'

    return {
      scenarioName: this.scenarioName,
      totalTests: this.testCases.length,
      passedTests,
      failedTests,
      testCases: this.testCases,
      overallResult,
    }
  }

  /**
   * レガシー形式（SUCCESS/ERROR）への変換
   */
  toLegacyResult(): string {
    const result = this.getScenarioResult()
    return result.overallResult === 'FAIL' ? ERROR : SUCCESS
  }

  /**
   * 結果サマリーを出力
   */
  printSummary(): void {
    const result = this.getScenarioResult()

    console.info(`=== ${result.scenarioName} Summary ===`)
    console.info(`Total: ${result.totalTests}, Pass: ${result.passedTests}, Fail: ${result.failedTests}}`)
    console.info(`Overall Result: ${result.overallResult}`)

    if (result.failedTests > 0) {
      console.info('Failed Tests:')
      result.testCases
        .filter((tc) => tc.result === 'FAIL')
        .forEach((tc) => console.info(`  - ${tc.testName}: ${tc.message}`))
    }
  }
}

/**
 * エラーコード検証のためのヘルパー関数
 */
export class ErrorCodeValidator {
  /**
   * 通常戻りと例外の両経路でエラーコードを検証
   * @param testName - テスト名
   * @param expectedErrorCode - 期待するエラーコード
   * @param asyncOperation - テスト対象の非同期処理
   * @param resultManager - 結果管理インスタンス
   * @returns テスト結果
   */
  static async validateBothPaths(
    testName: string,
    expectedErrorCode: string,
    asyncOperation: () => Promise<string>,
    resultManager: TestResultManager,
  ): Promise<TestResult> {
    try {
      // 通常戻りパス
      const result = await asyncOperation()
      return resultManager.recordErrorCodeTest(testName, expectedErrorCode, result)
    } catch (error) {
      // 例外パス
      const errorStr = String(error)
      return resultManager.recordErrorCodeTest(testName, expectedErrorCode, errorStr)
    }
  }

  /**
   * 環境依存テストの実行
   * @param testName - テスト名
   * @param testFunction - テスト実行関数
   * @param resultManager - 結果管理インスタンス
   * @returns テスト結果
   */
  static async executeConditionalTest<T>(
    testName: string,
    testFunction: () => Promise<T>,
    resultManager: TestResultManager,
  ): Promise<TestResult> {
    try {
      await testFunction()
      // テスト関数内でresultManagerを使って結果を記録することを想定
      return 'PASS'
    } catch (error) {
      return resultManager.recordFail(testName, `Test execution failed: ${String(error)}`)
    }
  }
}
