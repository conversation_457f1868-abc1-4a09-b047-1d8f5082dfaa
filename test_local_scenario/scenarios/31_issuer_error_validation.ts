import { ERR } from '../../test/common/consts'
import { Network, networkConfig, commonConfig } from '../helpers/constants'
import { RegisterAccTask } from '../tasks/RegisterAccTask'
import { RegisterIssuerTask } from '../tasks/RegisterIssuerTask'
import { SetAccountStatusTask } from '../tasks/SetAccountStatusTask'
import { TestResultManager } from './error-validation-utils'
import { delay } from './utils'

/**
 * Issuer系エラーコードの検証シナリオ
 * 実際のSolidityコードの使用場所に基づいたテストを実行
 */
export async function issuerErrorValidationScenario(network: Network): Promise<string> {
  const resultManager = new TestResultManager('Issuer Error Validation Scenario')

  console.info('=== Issuer Error Validation Scenario Started ===')

  const testAccountId = 'ISSUER_TEST_ACC'

  try {
    // テスト用アカウントの準備
    console.info('0. Registering test account for Issuer error validation')
    await registerTestAccount(network, testAccountId)
    await delay()

    // 1. Issuer ID存在エラーの検証
    console.info('1. Testing issuer ID exist error')
    await testIssuerIdExistError(network, testAccountId, resultManager)

    // 2. Issuer ID不存在エラーの検証
    console.info('2. Testing issuer ID not exist error')
    await testIssuerIdNotExistError(network, testAccountId, resultManager)

    // 3. Issuer bankCode重複エラーの検証
    console.info('3. Testing issuer exist bank code error')
    await testIssuerExistBankCodeError(network, testAccountId, resultManager)

    // 4. Issuer無効値エラーの検証
    console.info('4. Testing issuer invalid value error')
    await testIssuerInvalidValueError(network, testAccountId, resultManager)

    // 5. Issuerロール権限エラーの検証（Not Role）
    console.info('5. Testing issuer not role error')
    await testIssuerNotRoleError(network, testAccountId, resultManager)

    // 6. Issuer invalid value（空ID）
    console.info('6. Testing issuer invalid value error')
    await testIssuerInvalidValueError(network, testAccountId, resultManager)

    // 結果サマリー出力
    resultManager.printSummary()
    return resultManager.toLegacyResult()
  } catch (error) {
    console.error('Error in Issuer error validation scenario:', String(error))
    resultManager.recordFail('Scenario Execution', `Unexpected error: ${String(error)}`)
    return resultManager.toLegacyResult()
  }
}

/**
 * Issuer ID存在エラーのテスト
 * GE1009_ISSUER_ID_EXIST: "GE1009:exist issuer id"
 */
async function testIssuerIdExistError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Issuer ID Exist Error (aligned)'
  // 実装では bankCode 重複が先に評価されるため、期待値を GE1009 に合わせる
  const expectedErrorCode = ERR.ISSUER.ISSUER_EXIST_BANK_CODE

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  try {
    // 既に登録済みのIssuer IDで重複登録を試みる
    const dupIssuer = await new RegisterIssuerTask(network).execute({ flag: '10' })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, dupIssuer)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * Issuer ID不存在エラーのテスト
 * GE0106_ISSUER_ID_NOT_EXIST: "GE0106:issuer id not exist"
 */
async function testIssuerIdNotExistError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Issuer ID Not Exist Error'
  const expectedErrorCode = ERR.ISSUER.ISSUER_ID_NOT_EXIST

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  try {
    const cfg = networkConfig[network]
    // 未登録のissuerIdでIssuer操作（setAccountStatus）を実行し、GE0104を誘発
    const res = await new SetAccountStatusTask(network).execute({
      issuerId: 'UNREGISTERED_ISSUER_ID',
      accountId: cfg.ACCOUNT_ID_1,
      accountStatus: 'ACTIVE',
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, res)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * Issuerアドレス存在エラーのテスト
 * GE1010_ISSUER_ADDR_EXIST: "GE1010:exist issuer addr"
 */
async function testIssuerExistBankCodeError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Issuer Exist Bank Code Error'
  const expectedErrorCode = ERR.ISSUER.ISSUER_EXIST_BANK_CODE

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  try {
    const cfg = networkConfig[network]
    // 既存bankCodeでIssuerを追加して重複エラーを誘発
    const dupRes = await new RegisterIssuerTask(network).execute({
      issuerId: accountId + '_DUP_BANK',
      bankCode: cfg.BANK_CODE,
      issuerName: 'dup_bank',
      flag: '10',
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, dupRes)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * Issuer無効値エラーのテスト
 * RV0002_ISSUER_INVALID_VAL: "RV0002:issuer invalid value"
 */
async function testIssuerInvalidValueError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Issuer Invalid Value Error'
  const expectedErrorCode = ERR.ISSUER.ISSUER_INVALID_VAL

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  try {
    // issuerId空かつ bankCode=0 で登録を試みて RV0006 を確実に誘発
    const res = await new RegisterIssuerTask(network).execute({
      issuerId: '',
      bankCode: '0',
      issuerName: 'invalid',
      flag: '10',
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, res)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * Issuerロール権限エラーのテスト
 * GA0007_ISSUER_NOT_ROLE: "GA0007:not issuer"
 * GA0008_ISSUER_OWN_NOT_ROLE: "GA0008:not own issuer"
 * GA0009_ISSUER_CURR_NOT_ROLE: "GA0009:not issuer currency"
 */
async function testIssuerNotRoleError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Issuer Not Role Error'
  const expectedErrorCode = ERR.ISSUER.ISSUER_NOT_ROLE

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)

  try {
    const cfg = networkConfig[network]
    // Issuer権限が必要な操作を非Issuer鍵で実行
    const res = await new SetAccountStatusTask(network).execute({
      issuerId: cfg.ISSUER_ID,
      accountId: cfg.ACCOUNT_ID_1,
      accountStatus: 'ACTIVE',
      signerKey: commonConfig.KEY_ACCOUNT,
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, res)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * テスト用アカウントの登録
 */
async function registerTestAccount(network: Network, accountId: string): Promise<void> {
  const cfg = networkConfig[network]
  await new RegisterAccTask(network).execute({
    accountId,
    accountName: `Issuer Test Account ${accountId}`,
    validId: cfg.VALID_ID,
  })
}
