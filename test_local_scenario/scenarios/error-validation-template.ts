/**
 * @fileoverview エラーコード検証シナリオテンプレート
 *
 * SSOT化と新しい検証方式のテンプレート
 * 他のシナリオファイルもこのパターンに従って更新すること
 */

/**
 * 方針チェックリスト実装の要点
 *
 * ✅ 1. SSOT化: ERR from '../../test/common/consts' のみ使用
 * ✅ 2. 必須FAIL化: recordErrorCodeTest で観測できなければ FAIL
 * ✅ 3. 例外検証: validateBothPaths で通常戻り・例外両方チェック
 * ✅ 4. 環境分離: executeConditionalTest で Skip/必須を明確に分離
 * ✅ 5. PASS/FAIL: TestResult型で統一
 * ✅ 6. コード抽出: ErrorCodeExtractor で ^[A-Z]{2}\d{4}: 部分のみ比較
 * ✅ 7. 個別判定: successCount方式ではなく各テストで個別合否
 */

/**
 * レガシーコードからの移行手順:
 *
 * 1. import文の修正:
 *    - 独自ERROR_CODES定数を削除
 *    - ERR from '../../test/common/consts' をimport
 *    - TestResultManager, ErrorCodeValidator をimport
 *
 * 2. メイン関数の修正:
 *    - TestResultManager インスタンス作成
 *    - 個別テスト関数にresultManagerを渡す
 *    - 最後にprintSummary()とtoLegacyResult()で結果返却
 *
 * 3. 個別テスト関数の修正:
 *    - 戻り値をPromise<void>に変更
 *    - resultManager: TestResultManagerパラメータ追加
 *    - ERR.CATEGORY.ERROR_NAMEでエラーコード参照
 *    - resultManager.recordErrorCodeTest()で結果記録
 *    - try/catch両方でrecordErrorCodeTest()呼び出し
 *
 * 4. 観測失敗時の処理:
 *    - 「仮の成功」return SUCCESを削除
 *    - 観測できない場合は recordFail
 */
