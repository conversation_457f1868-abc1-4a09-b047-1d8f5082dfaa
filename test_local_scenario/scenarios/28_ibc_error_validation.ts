import type { HardhatRuntimeEnvironment } from 'hardhat/types'
import { ERR } from '../../test/common/consts'
import { Network, networkConfig } from '../helpers/constants'
import type { BaseTaskArguments } from '../helpers/task-arguments'
import { BaseTask } from '../tasks/base-task'
import { RegisterAccTask } from '../tasks/RegisterAccTask'
import { TestResultManager } from './error-validation-utils'
import { delay } from './utils'

/**
 * IBC系エラーコードの検証シナリオ
 * 実際のSolidityコードの使用場所に基づいたテストを実行
 */
export async function ibcErrorValidationScenario(network: Network): Promise<string> {
  const resultManager = new TestResultManager('IBC Error Validation Scenario')

  console.info('=== IBC Error Validation Scenario Started ===')

  const testAccountId = 'IBC_TEST_ACC'

  try {
    // テスト用アカウントの準備
    console.info('Registering test account for IBC error validation')
    await registerTestAccount(network, testAccountId)
    await delay()

    // 1. IBC無効値エラーの検証
    console.info('1. Testing IBC invalid value error')
    await testIBCInvalidValueError(network, testAccountId, resultManager)

    // 2. IBCコントラクトでないエラーの検証
    console.info('2. Testing not IBC contract error')
    await testNotIBCContractError(testAccountId, resultManager)

    // 結果サマリー出力
    resultManager.printSummary()
    return resultManager.toLegacyResult()
  } catch (error) {
    console.error('Error in IBC error validation scenario:', String(error))
    resultManager.recordFail('Scenario Execution', `Unexpected error: ${String(error)}`)
    return resultManager.toLegacyResult()
  }
}

/**
 * IBC無効値エラーのテスト
 */
async function testIBCInvalidValueError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'IBC Invalid Value Error'
  const expectedErrorCode = ERR.IBC.IBC_INVALID_VAL

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)

  try {
    // JPYTokenTransferBridge.transfer を from/to 同一ゾーンで呼び出し、
    // require(_isNativeRegion(from) != _isNativeRegion(to)) により RV0010 を誘発
    const finZone = String(networkConfig[Network.LocalFin].ZONE_ID)
    const out = await runTask(network, 'transfer', {
      accountId,
      fromZoneId: finZone,
      toZoneId: finZone, // 同一ゾーンにすることで invalid value を強制
      amount: '1',
      // networkConfig には TIMEOUT_HEIGHT は存在しないため固定値を使用
      timeoutHeight: '1000000',
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, out)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * IBCコントラクトでないエラーのテスト
 */
async function testNotIBCContractError(accountId: string, resultManager: TestResultManager): Promise<void> {
  const testName = 'Not IBC Contract Error'
  const expectedErrorCode = ERR.IBC.NOT_IBC_CONTRACT

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)

  try {
    // IBCToken.issueVoucher をEOAから直接呼び出し、
    // msg.sender != JPYTokenTransferBridge となるため GA0022 を誘発
    const hreModule = await import('hardhat')
    const hre = hreModule as unknown as HardhatRuntimeEnvironment
    const { ethers, deployments } = hre
    const dep = await deployments.get('IBCToken')
    const ibcToken = await ethers.getContractAt('IBCToken', dep.address)

    // ethers v5/v6 の両方に対応
    type EthersV5 = { utils: { formatBytes32String: (s: string) => string } }
    type EthersV6 = { encodeBytes32String: (s: string) => string }
    const isV5 = (e: unknown): e is EthersV5 => typeof (e as EthersV5)?.utils?.formatBytes32String === 'function'
    const isV6 = (e: unknown): e is EthersV6 => typeof (e as EthersV6)?.encodeBytes32String === 'function'
    const fmtBytes32 = (s: string) =>
      isV5(ethers) ? ethers.utils.formatBytes32String(s) : isV6(ethers) ? ethers.encodeBytes32String(s) : s

    const accountBytes = fmtBytes32('ibc_not_ibc_sender')
    const traceId = fmtBytes32('trace')

    try {
      const tx = await ibcToken.issueVoucher(accountBytes, 1, traceId)
      await tx.wait()
      // ここまで到達しないはずだが、万一到達した場合も出力を検証する
      resultManager.recordErrorCodeTest(testName, expectedErrorCode, 'tx mined without revert')
    } catch (e) {
      resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(e))
    }
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

// 簡易タスク実行ヘルパ（anyを使わない）
async function runTask(network: Network, taskName: string, args: Record<string, string>): Promise<string> {
  class QuickTask extends BaseTask<BaseTaskArguments> {
    filePath = __filename
    constructor(network: Network, taskName: string) {
      super(network, taskName)
    }
    protected getDefaultArguments(): Partial<BaseTaskArguments> {
      return {}
    }
  }
  return await new QuickTask(network, taskName).execute(args)
}

/**
 * テスト用アカウントの登録
 */
async function registerTestAccount(network: Network, accountId: string): Promise<void> {
  const cfg = networkConfig[network]
  await new RegisterAccTask(network).execute({
    accountId,
    accountName: `IBC Test Account ${accountId}`,
    validId: cfg.VALID_ID,
  })
}
