import { ERR } from '../../test/common/consts'
import { Network, networkConfig } from '../helpers/constants'
import { RegisterAccTask } from '../tasks/RegisterAccTask'
import { RegisterValidTask } from '../tasks/RegisterValidTask'
import { TestResultManager } from './error-validation-utils'
import { delay } from './utils'

/**
 * Validator系エラーコードの検証シナリオ
 * 実際のSolidityコードの使用場所に基づいたテストを実行
 */
export async function validatorErrorValidationScenario(network: Network): Promise<string> {
  const resultManager = new TestResultManager('Validator Error Validation Scenario')

  console.info('=== Validator Error Validation Scenario Started ===')

  const testAccountId = 'VALIDATOR_TEST_ACC'

  try {
    // テスト用アカウントの準備
    console.info('1. Registering test account for Validator error validation')
    await registerTestAccount(network, testAccountId)
    await delay()

    // 1. Validator ID存在エラーの検証
    console.info('1. Testing validator ID exist error')
    await testValidatorIdExistError(network, testAccountId, resultManager)

    // 2. Validator無効値エラーの検証
    console.info('2. Testing validator invalid value error')
    await testValidatorInvalidValueError(testAccountId, resultManager)

    // 3. validator invalid value（空ID）
    console.info('3. Testing validator invalid value error')
    await testValidatorInvalidValueConcrete(network, resultManager)

    // 4. validator id not exist（未登録ID）
    console.info('4. Testing validator id not exist error')
    await testValidatorIdNotExistConcrete(network, resultManager)

    // 結果サマリー出力
    resultManager.printSummary()
    return resultManager.toLegacyResult()
  } catch (error) {
    console.error('Error in Validator error validation scenario:', String(error))
    resultManager.recordFail('Scenario Execution', `Unexpected error: ${String(error)}`)
    return resultManager.toLegacyResult()
  }
}

/**
 * Validator ID存在エラーのテスト
 * GE1014_VALIDATOR_ID_EXIST: "GE1014:exist id"
 */
async function testValidatorIdExistError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Validator ID Exist Error'
  const expectedErrorCode = ERR.VALID.VALIDATOR_ID_EXIST

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  try {
    // 既に登録済みのValidator IDで重複登録を試みる
    const dupValid = await new RegisterValidTask(network).execute({ flag: '10' })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, dupValid)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * Validator無効値エラーのテスト
 * RV0009_VALIDATOR_INVALID_VAL: "RV0009:validator invalid value"
 */
async function testValidatorInvalidValueError(accountId: string, resultManager: TestResultManager): Promise<void> {
  const testName = 'Validator Invalid Value Error (abstract)'
  const expectedErrorCode = ERR.VALID.VALIDATOR_INVALID_VAL

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  // 旧テストは抽象に留め、具体テストで実エラーを取得
  resultManager.addTestCase({ testName, result: 'PASS', message: 'Covered by concrete test' })
}

// 具体的な invalid value（validatorId=0x00）
async function testValidatorInvalidValueConcrete(network: Network, resultManager: TestResultManager): Promise<void> {
  const testName = 'Validator Invalid Value Error'
  const expectedErrorCode = ERR.VALID.VALIDATOR_INVALID_VAL
  try {
    const out = await new RegisterValidTask(network).execute({ validId: '', flag: '10' })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, out)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

// 具体的な id not exist（未登録validatorIdでAccount紐付けを要求）
async function testValidatorIdNotExistConcrete(network: Network, resultManager: TestResultManager): Promise<void> {
  const testName = 'Validator ID Not Exist Error (concrete)'
  const expectedErrorCode = ERR.VALID.VALIDATOR_ID_NOT_EXIST
  try {
    const out = await new RegisterValidTask(network).execute({ validId: 'UNREGISTERED_VALID', flag: '01' })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, out)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * テスト用アカウントの登録
 */
async function registerTestAccount(network: Network, accountId: string): Promise<void> {
  const cfg = networkConfig[network]
  await new RegisterAccTask(network).execute({
    accountId,
    accountName: `Validator Test Account ${accountId}`,
    validId: cfg.VALID_ID,
  })
}
