import { ERR } from '../../test/common/consts'
import { Network, networkConfig } from '../helpers/constants'
import { BaseTask } from '../tasks/base-task'
import { RegisterAccTask } from '../tasks/RegisterAccTask'
import { TestResultManager } from './error-validation-utils'
import { delay } from './utils'

/**
 * TransferProxy系エラーコードの検証シナリオ
 * 実際のSolidityコードの使用場所に基づいたテストを実行
 */
export async function transferProxyErrorValidationScenario(network: Network): Promise<string> {
  const resultManager = new TestResultManager('TransferProxy Error Validation Scenario')

  console.info('=== TransferProxy Error Validation Scenario Started ===')

  const testAccountId = 'TRANSFERPROXY_TEST_ACC'

  try {
    // テスト用アカウントの準備
    console.info('Registering test account for TransferProxy error validation')
    await registerTestAccount(network, testAccountId)
    await delay()

    // TransferProxy 依存を避け、直接Token呼び出しで GA0005:not provider contract を検証
    console.info('1. Testing not provider contract via direct Token call')
    await testNotProviderContract(network, resultManager)

    // 結果サマリー出力
    resultManager.printSummary()
    return resultManager.toLegacyResult()
  } catch (error) {
    console.error('Error in TransferProxy error validation scenario:', String(error))
    resultManager.recordFail('Scenario Execution', `Unexpected error: ${String(error)}`)
    return resultManager.toLegacyResult()
  }
}

/**
 * カスタムコントラクト不存在エラーのテスト
 * GE0111_CUSTOM_CONTRACT_NOT_EXIST: "GE0111:custom contract not exist"
 *
 * 使用場所: TransferProxy.sol.delegateTransfer()
 * 条件: require(info.contractAddr != address(0), Error.GE0111_CUSTOM_CONTRACT_NOT_EXIST)
 * 誘発方法: 未登録のカスタムコントラクトで代理転送を実行
 */
async function testNotProviderContract(network: Network, resultManager: TestResultManager): Promise<void> {
  const testName = 'Not Provider Contract Error (direct Token.getToken)'
  const expectedErrorCode = ERR.PROV.NOT_PROVIDER_CONTRACT
  try {
    type BaseArgs = import('../helpers/task-arguments').BaseTaskArguments
    class QuickTask extends BaseTask<BaseArgs> {
      filePath = __filename
      constructor(n: Network) {
        super(n, 'tokenGetDirect')
      }
      protected getDefaultArguments(): Partial<BaseArgs> {
        return {}
      }
    }
    const out = await new QuickTask(network).execute()
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, out)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * テスト用アカウントの登録
 */
async function registerTestAccount(network: Network, accountId: string): Promise<void> {
  const cfg = networkConfig[network]
  await new RegisterAccTask(network).execute({
    accountId,
    accountName: `TransferProxy Test Account ${accountId}`,
    validId: cfg.VALID_ID,
  })
}
