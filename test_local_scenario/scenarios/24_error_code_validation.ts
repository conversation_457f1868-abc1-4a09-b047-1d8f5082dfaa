import { ERR } from '../../test/common/consts'
import { Network, networkConfig } from '../helpers/constants'
import { AddBizZoneToIssuer } from '../tasks/AddBizZoneToIssuer'
import { BurnTokenTask } from '../tasks/BurnTokenTask'
import { CheckBurnTask } from '../tasks/CheckBurnTask'
import { CheckMintTask } from '../tasks/CheckMintTask'
import { CheckSyncAccountTask } from '../tasks/CheckSyncAccountTask'
import { CheckTransactionTask } from '../tasks/CheckTransactionTask'
import { MintTokenTask } from '../tasks/MintTokenTask'
import { ModTokenLimitTask } from '../tasks/ModTokenLimitTask'
import { RegisterAccTask } from '../tasks/RegisterAccTask'
import { SetAccountStatusTask } from '../tasks/SetAccountStatusTask'
import { SetActiveBusinessAccountWithZoneTask } from '../tasks/SetActiveBusinessAccountWithZoneTask'
import { SyncAccountTask } from '../tasks/SyncAccountTask'
import { TransferTask } from '../tasks/TransferTask'
import { TestResultManager } from './error-validation-utils'
import { delay } from './utils'

const STRICT_LIMIT_VALUES = {
  mint: 10000,
  burn: 10000,
  charge: 10000,
  discharge: 10000,
  transfer: 10000,
  cumulative: {
    total: 1000000,
    mint: 1049,
    burn: 120,
    charge: 50000,
    discharge: 50000,
    transfer: 120,
  },
}

/**
 * 新しいエラーコードシステムの検証シナリオ
 * 更新されたエラーコード定数が正しく返されることを確認
 */
export async function errorCodeValidationScenario(network: Network): Promise<string> {
  const resultManager = new TestResultManager('Error Code Validation Scenario')

  console.info('=== Error Code Validation Scenario Started ===')

  const testAccountId = 'ERROR_CODE_TEST_ACC'
  const testAccountId2 = 'ERROR_CODE_TEST_ACC_2'

  try {
    // 1. アカウント存在チェックエラーの検証
    console.info('1. Testing account not exists error codes')
    await testAccountNotExistsError(network, 'NON_EXISTENT_ACC', resultManager)

    // 2. テスト用アカウントの準備
    console.info('2. Registering test accounts')
    await registerTestAccount(network, testAccountId)
    await registerTestAccount(network, testAccountId2)
    await delay()

    // 3. アカウント認証エラーの検証
    console.info('3. Testing account authorization error codes')
    await testAccountAuthorizationError(network, testAccountId, resultManager)

    // 4. 日次制限超過エラーの検証
    console.info('4. Testing daily limit exceeded error codes')
    await testDailyLimitExceededErrors(network, testAccountId, resultManager)

    // 5. アカウント状態関連エラーの検証
    console.info('5. Testing account status error codes')
    await testAccountStatusErrors(network, testAccountId2, resultManager)

    // 6. 異なる発行者間取引エラーの検証
    console.info('6. Testing different issuer transaction error codes')
    await testDifferentIssuerError(network, testAccountId, resultManager)

    // 結果サマリー出力
    resultManager.printSummary()
    return resultManager.toLegacyResult()
  } catch (error) {
    console.error('Error in error code validation scenario:', String(error))
    resultManager.recordFail('Scenario Execution', `Unexpected error: ${String(error)}`)
    return resultManager.toLegacyResult()
  }
}

/**
 * アカウント不存在エラーコードのテスト
 */
async function testAccountNotExistsError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Account Not Exists Error'
  const expectedErrorCode = ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST

  console.info(`Testing account not exists error for: ${accountId}`)

  try {
    // 未登録アカウントに対して状態変更を要求し、GE0105 を取得
    const cfg = networkConfig[network]
    const out = await new SetAccountStatusTask(network).execute({
      accountId: accountId,
      accountStatus: 'ACTIVE',
      issuerId: cfg.ISSUER_ID,
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, out)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * アカウント認証エラーコードのテスト
 */
async function testAccountAuthorizationError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Different Issuer Transaction Error'
  const expectedErrorCode = ERR.ACCOUNT.FROM_AND_TO_ACCOUNT_ISSUERS_ARE_DIFFERENT

  console.info(`Testing account authorization errors for: ${accountId}`)

  try {
    const unlinkAccountId = networkConfig[Network.LocalFin].UNLINKED_ISSUER_ACCOUNT_ID

    const checkResult = await new CheckTransactionTask(network).execute({
      sendAccountId: accountId,
      fromAccountId: accountId,
      toAccountId: unlinkAccountId,
    })

    resultManager.recordErrorCodeTest(testName, expectedErrorCode, checkResult)
  } catch (error) {
    const errorStr = String(error)
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, errorStr)
  }
}

/**
 * 日次制限超過エラーコードのテスト
 */
async function testDailyLimitExceededErrors(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  console.info(`Testing daily limit exceeded errors for: ${accountId}`)

  // 制限値を設定
  await new ModTokenLimitTask(network).execute({
    accountId,
    limitValues: JSON.stringify(STRICT_LIMIT_VALUES),
  })

  // 十分な残高を設定
  await new MintTokenTask(network).execute({ accountId, amount: '1000' })
  await delay()

  // Mint制限超過テスト
  await new MintTokenTask(network).execute({ accountId, amount: '100' })
  try {
    const mintCheckResult = await new CheckMintTask(network).execute({ accountId, amount: '50' })
    resultManager.recordErrorCodeTest(
      'Daily Mint Limit Error',
      ERR.FINACCOUNT.EXCEEDED_DAILY_MINT_LIMIT,
      mintCheckResult,
    )
  } catch (error) {
    resultManager.recordErrorCodeTest('Daily Mint Limit Error', ERR.FINACCOUNT.EXCEEDED_DAILY_MINT_LIMIT, String(error))
  }

  // Burn制限超過テスト
  await new BurnTokenTask(network).execute({ accountId, amount: '100' })
  try {
    const burnCheckResult = await new CheckBurnTask(network).execute({ accountId, amount: '50' })
    resultManager.recordErrorCodeTest(
      'Daily Burn Limit Error',
      ERR.FINACCOUNT.EXCEEDED_DAILY_BURN_LIMIT,
      burnCheckResult,
    )
  } catch (error) {
    // 環境によってはガード順序により直接4103が返るため、ここでも4103を期待
    resultManager.recordErrorCodeTest('Daily Burn Limit Error', ERR.FINACCOUNT.EXCEEDED_DAILY_BURN_LIMIT, String(error))
  }
}
/**
 * アカウント状態関連エラーコードのテスト
 */
async function testAccountStatusErrors(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  console.info(`Testing account status errors for: ${accountId}`)

  // まずFin残高を設定
  await new MintTokenTask(network).execute({ accountId, amount: '1000' })
  await delay()

  // DeleteBizZoneToIssuer が先行シナリオで実行済みの場合に備え、
  // 必要な発行体-ビズゾーンの関連付けを明示的に再設定しておく
  try {
    await new AddBizZoneToIssuer(Network.LocalFin).execute({
      issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
      zoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    })
    await delay()
  } catch (e) {
    // 既に関連付け済みなどで失敗した場合は無視（この後の処理で検証する）
  }

  // BizZoneアカウントを同期しアクティブ化（まずFin側でsync申請）
  const cfg = networkConfig[network]
  await new CheckSyncAccountTask(Network.LocalFin).execute({
    accountId,
    validId: cfg.VALID_ID,
    zoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    accountStatus: 'applying',
  })
  await new SyncAccountTask(Network.LocalBiz).execute({ accountId })
  await delay()
  await new SetActiveBusinessAccountWithZoneTask(Network.LocalFin).execute({ accountId })
  // 伝播待ち（Biz側のアクティブ反映）
  await delay()
  await delay()

  // BizZoneへ少額を転送してBiz残高を持たせる
  await new TransferTask(Network.LocalFin).execute({
    accountId,
    fromZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    amount: '50',
  })
  await delay()

  // アカウント残高非ゼロエラーのテスト（terminating時のBiz残高!=0）
  try {
    const cfg = networkConfig[network]
    const check = await new CheckSyncAccountTask(Network.LocalFin).execute({
      accountId,
      zoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
      validId: cfg.VALID_ID,
      accountStatus: 'terminating',
    })
    resultManager.recordErrorCodeTest('Account Balance Not Zero Error', ERR.ACCOUNT.ACCOUNT_BALANCE_NOT_ZERO, check)
  } catch (error) {
    resultManager.recordErrorCodeTest(
      'Account Balance Not Zero Error',
      ERR.ACCOUNT.ACCOUNT_BALANCE_NOT_ZERO,
      String(error),
    )
  }
}

/**
 * 異なる発行者間取引エラーのテスト
 */
async function testDifferentIssuerError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Different Issuer Transaction Error'
  const expectedErrorCode = ERR.ACCOUNT.FROM_AND_TO_ACCOUNT_ISSUERS_ARE_DIFFERENT

  console.info(`Testing different issuer transaction error for: ${accountId}`)

  try {
    const unlinkAccountId = networkConfig[Network.LocalFin].UNLINKED_ISSUER_ACCOUNT_ID

    const checkResult = await new CheckTransactionTask(network).execute({
      sendAccountId: accountId,
      fromAccountId: accountId,
      toAccountId: unlinkAccountId,
      amount: '100',
    })

    resultManager.recordErrorCodeTest(testName, expectedErrorCode, checkResult)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * テスト用アカウントの登録
 */
async function registerTestAccount(network: Network, accountId: string): Promise<void> {
  const config = networkConfig[network]

  await new RegisterAccTask(network).execute({
    accountId,
    accountName: `Error Code Test Account ${accountId}`,
    validId: config.VALID_ID,
  })
}
