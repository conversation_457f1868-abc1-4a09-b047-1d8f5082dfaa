import { ERR } from '../../test/common/consts'
import { Network, networkConfig, commonConfig } from '../helpers/constants'
import type { BaseTaskArguments } from '../helpers/task-arguments'
import { BaseTask } from '../tasks/base-task'
import { RegisterAccTask } from '../tasks/RegisterAccTask'
import { SetAccountStatusTask } from '../tasks/SetAccountStatusTask'
import { TestResultManager } from './error-validation-utils'
import { delay } from './utils'

/**
 * AccessCtrl系エラーコードの検証シナリオ
 * 実際のSolidityコードの使用場所に基づいたテストを実行
 */
export async function accessCtrlErrorValidationScenario(network: Network): Promise<string> {
  const resultManager = new TestResultManager('AccessCtrl Error Validation Scenario')

  console.info('=== AccessCtrl Error Validation Scenario Started ===')

  const testAccountId = 'ACCESSCTRL_TEST_ACC'

  try {
    // テスト用アカウントの準備
    console.info('1. Registering test account for AccessCtrl error validation')
    await registerTestAccount(network, testAccountId)
    await delay()

    // 署名タイムアウトエラーの検証
    console.info('2. Testing signature timeout error')
    await testSignatureTimeoutError(network, testAccountId, resultManager)

    // 不正ロール（RV0001）: addRole に ADMIN_ROLE を指定して誘発
    console.info('3. Testing bad role error (addRole with ADMIN_ROLE)')
    await testBadRoleByAddRoleInvalid(network, resultManager)

    // 不正署名エラーの検証
    console.info('4. Testing bad signature error')
    await testBadSignatureError(network, testAccountId, resultManager)

    // ロールなし（GA0002）: checkRole に role=0 を指定して誘発
    console.info('5. Testing no role error (checkRole with role=0)')
    await testNoRoleError(network, resultManager)

    // 管理者権限なしエラーの検証
    console.info('6. Testing not admin role error')
    await testNotAdminRoleError(network, testAccountId, resultManager)

    // 結果サマリー出力
    resultManager.printSummary()
    return resultManager.toLegacyResult()
  } catch (error) {
    console.error('Error in AccessCtrl error validation scenario:', String(error))
    resultManager.recordFail('Scenario Execution', `Unexpected error: ${String(error)}`)
    return resultManager.toLegacyResult()
  }
}

/**
 * 署名タイムアウトエラーのテスト
 * GA0001_ACTRL_SIG_TIMEOUT: "GA0001:sig timeout"
 *
 * 使用場所: AccessCtrl.sol._checkRole() (Line 372)
 * 条件: if (deadline < block.timestamp)
 * 誘発方法: 期限切れのdeadlineパラメータで署名ベースの操作を実行
 */
async function testSignatureTimeoutError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Signature Timeout Error'
  const expectedErrorCode = ERR.ACTRL.ACTRL_SIG_TIMEOUT

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: AccessCtrl.sol._checkRole() Line 372 - deadline < block.timestamp')

  try {
    const cfg = networkConfig[network]
    const timeoutResult = await new SetAccountStatusTask(network).execute({
      accountId: accountId,
      accountStatus: 'ACTIVE',
      issuerId: cfg.ISSUER_ID,
      deadline: '1',
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, timeoutResult)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

/**
 * 不正署名エラーのテスト
 * GS0001_ACTRL_BAD_SIG: "GS0001:bad sig"
 *
 * 使用場所: AccessCtrl.sol._checkRole() Line 378
 * 条件: if (addr == address(0)) - 署名復元失敗時
 * 誘発方法: 破損した署名データで署名ベースの操作を実行
 */
async function testBadSignatureError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Bad Signature Error'
  const expectedErrorCode = ERR.ACTRL.ACTRL_BAD_SIG

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: AccessCtrl.sol._checkRole() Line 378 - if (addr == address(0))')

  // SetAccountStatusTaskでの署名検証失敗テスト
  try {
    const cfg = networkConfig[network]
    const badSigResult = await new SetAccountStatusTask(network).execute({
      accountId: accountId,
      accountStatus: 'ACTIVE',
      issuerId: cfg.ISSUER_ID,
      rawSig: ('0x' + '00'.repeat(65)) as string,
    })
    resultManager.recordErrorCodeTest(testName + ' (Status Change)', expectedErrorCode, badSigResult)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName + ' (Status Change)', expectedErrorCode, String(error))
  }
}

/**
 * ロールなしエラーのテスト
 * GA0002_ACTRL_NOT_ROLE: "GA0002:not role"
 *
 * 使用場所: AccessCtrl.sol._checkRole() Line 367
 * 条件: if (role == 0) - ロールが0（無効）の場合
 * 誘発方法: role=0で署名ベースの操作を実行
 */
// checkRole(role=0) で GA0002 を誘発
async function testNoRoleError(network: Network, resultManager: TestResultManager): Promise<void> {
  const testName = 'No Role Error'
  const expectedErrorCode = ERR.ACTRL.ACTRL_NOT_ROLE
  try {
    // 直接 Hardhat タスク accessCtrlCheckRole を呼ぶ
    const out = await runTask(network, 'accessCtrlCheckRole', { role: '0x' + '00'.repeat(32) })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, out)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

// addRole(role=ADMIN_ROLE) で RV0001 を誘発
async function testBadRoleByAddRoleInvalid(network: Network, resultManager: TestResultManager): Promise<void> {
  const testName = 'Bad Role Error (addRole with ADMIN_ROLE)'
  const expectedErrorCode = ERR.ACTRL.ACTRL_BAD_ROLE
  try {
    const out = await runTask(network, 'accessCtrlAddRole', {
      eoa: '0x90F79bf6EB2c4f870365E785982E1f101E93b906',
      roleName: 'ADMIN_ROLE',
    })
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, out)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName, expectedErrorCode, String(error))
  }
}

// 簡易タスク実行ヘルパ（anyを使わない型定義）

async function runTask(network: Network, taskName: string, args: Record<string, string>): Promise<string> {
  class QuickTask extends BaseTask<BaseTaskArguments> {
    filePath = __filename
    constructor(network: Network, taskName: string) {
      super(network, taskName)
    }
    protected getDefaultArguments(): Partial<BaseTaskArguments> {
      return {}
    }
  }
  return await new QuickTask(network, taskName).execute(args)
}

/**
 * 管理者権限なしエラーのテスト
 * GA0003_ACTRL_NOT_ADMIN_ROLE: "GA0003:accessctrl not admin"
 *
 * 誘発方法: 管理者権限が必要な操作を非管理者で実行
 */
async function testNotAdminRoleError(
  network: Network,
  accountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  const testName = 'Not Admin Role Error'
  const expectedErrorCode = ERR.ACTRL.ACTRL_NOT_ADMIN_ROLE // GA0003:accessctrl not admin

  console.info(`Testing ${expectedErrorCode} for account: ${accountId}`)
  console.info('Location: AccessCtrl.addRole() (admin required path)')

  // AccessCtrl.addRole を非Admin鍵で呼び出し、GA0003 を誘発
  try {
    const out = await runTask(network, 'accessCtrlAddRole', {
      eoa: '0x90F79bf6EB2c4f870365E785982E1f101E93b906',
      roleName: 'USER_ROLE',
      adminKey: commonConfig.KEY_ACCOUNT, // 非Admin鍵
    })
    resultManager.recordErrorCodeTest(testName + ' (addRole non-admin)', expectedErrorCode, out)
  } catch (error) {
    resultManager.recordErrorCodeTest(testName + ' (addRole non-admin)', expectedErrorCode, String(error))
  }
}

/**
 * テスト用アカウントの登録
 */
async function registerTestAccount(network: Network, accountId: string): Promise<void> {
  const cfg = networkConfig[network]
  await new RegisterAccTask(network).execute({
    accountId,
    accountName: `AccessCtrl Test Account ${accountId}`,
    validId: cfg.VALID_ID,
  })
}
