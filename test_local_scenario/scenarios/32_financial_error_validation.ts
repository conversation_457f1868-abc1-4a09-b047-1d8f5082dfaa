import { ERR } from '@test/common/consts'
import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { TestResultManager } from './error-validation-utils'
import { SetAccountStatusTask } from '@testLocalScenario/tasks/SetAccountStatusTask'
import { ModTokenLimitTask } from '@testLocalScenario/tasks/ModTokenLimitTask'
import { CheckMintTask } from '@testLocalScenario/tasks/CheckMintTask'
import { CheckBurnTask } from '@testLocalScenario/tasks/CheckBurnTask'
import { CheckTransactionTask } from '@testLocalScenario/tasks/CheckTransactionTask'
import { MintTokenTask } from '@testLocalScenario/tasks/MintTokenTask'
import { delay } from '@testLocalScenario/scenarios/utils'
import { CheckExchangeTask } from '@testLocalScenario/tasks/CheckExchangeTask'
import { SetActiveBusinessAccountWithZoneTask } from '@testLocalScenario/tasks/SetActiveBusinessAccountWithZoneTask'
import { CheckSyncAccountTask } from '@testLocalScenario/tasks/CheckSyncAccountTask'
import { SyncAccountTask } from '@testLocalScenario/tasks/SyncAccountTask'
import { TransferTask } from '@testLocalScenario/tasks/TransferTask'
import { RegisterAccTask } from '@testLocalScenario/tasks/RegisterAccTask'

/**
 * Financial系・RenewableEnergy系エラーコード検証メイン関数
 */
// 制限値設定
const STRICT_LIMIT_VALUES = {
  mint: 10000,
  burn: 10000,
  charge: 100,
  discharge: 50,
  transfer: 100,
  cumulative: {
    total: 1000000,
    mint: 1049,
    burn: 120,
    charge: 50000,
    discharge: 50000,
    transfer: 120,
  },
}
// 日次制限値設定
const DAILY_STRICT_LIMIT_VALUES = {
  mint: 10000,
  burn: 10000,
  charge: 10000,
  discharge: 10000,
  transfer: 10000,
  cumulative: {
    total: 1200,
    mint: 1049,
    burn: 120,
    charge: 500,
    discharge: 400,
    transfer: 120,
  },
}

export async function validateFinancialErrorCodes(network: Network): Promise<string> {
  const resultManager = new TestResultManager('Financial & RenewableEnergy Error Validation')

  console.info('=== Financial & RenewableEnergy Error Validation Started ===')

  const testAccountId = `ERROR_CODE_TEST_FIN_ACC`
  const testToAccountId = `ERROR_CODE_TEST_TO_FIN_ACC`

  const testDailyAccountId = `ERROR_CODE_TEST_FIN_ACC_DAILY`
  const testToDailyAccountId = `ERROR_CODE_TEST_TO_FIN_ACC_DAILY`

  try {
    // テスト用アカウントの準備
    console.info('2. Registering test accounts')
    await registerTestAccount(network, testAccountId)
    await registerTestAccount(network, testToAccountId)
    await registerTestAccount(network, testDailyAccountId)
    await registerTestAccount(network, testToDailyAccountId)
    await delay()

    // 1. Financial Zone Account 上メモ：
    // 使用しないエラーコード（ゴミ）を削除する限エラーの検証
    console.info('1. Testing Financial Zone Account limit exceeded errors')
    await testFinancialLimitErrors(network, testAccountId, testToAccountId, resultManager)

    // 2. Financial Zone Account 日次上限エラーの検証
    console.info('2. Testing Financial Zone Account daily limit exceeded errors')
    await testFinancialDailyLimitErrors(network, testDailyAccountId, testToDailyAccountId, resultManager)

    // 結果サマリー出力
    resultManager.printSummary()
    return resultManager.toLegacyResult()
  } catch (error) {
    console.error('Financial & RenewableEnergy error validation failed:', error)
    resultManager.recordFail('Scenario Execution', `Unexpected error: ${String(error)}`)
    return resultManager.toLegacyResult()
  }
}

/**
 * テスト用アカウントの登録
 */
async function registerTestAccount(network: Network, accountId: string): Promise<void> {
  const config = networkConfig[network]

  await new RegisterAccTask(network).execute({
    accountId,
    accountName: `Error Code Test Account ${accountId}`,
    validId: config.VALID_ID,
  })
}

async function testFinancialLimitErrors(
  network: Network,
  accountId: string,
  toAccountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  console.info('Testing Financial Zone Account limit errors...')

  // 制限値を設定
  await new ModTokenLimitTask(network).execute({
    accountId,
    limitValues: JSON.stringify(STRICT_LIMIT_VALUES),
  })

  // Mint制限超過テスト
  const mintCheckResult = await new CheckMintTask(network).execute({ accountId, amount: '12000' })
  resultManager.recordErrorCodeTest('Mint Limit Error', ERR.FINACCOUNT.EXCEEDED_MINT_LIMIT, mintCheckResult)

  // Burn制限超過テスト
  const burnCheckResult = await new CheckBurnTask(network).execute({ accountId, amount: '12000' })
  resultManager.recordErrorCodeTest('Burn Limit Error', ERR.FINACCOUNT.EXCEEDED_BURN_LIMIT, burnCheckResult)

  // 十分な残高を設定
  await new MintTokenTask(network).execute({ accountId, amount: '1000' })
  await delay()

  // Transfer制限超過テスト
  const transferCheckResult = await new CheckTransactionTask(network).execute({
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: toAccountId,
    amount: '200',
  })
  resultManager.recordErrorCodeTest('Transfer Limit Error', ERR.FINACCOUNT.EXCEEDED_TRANSFER_LIMIT, transferCheckResult)

  // Fin側で同期申請（applying）
  await new CheckSyncAccountTask(Network.LocalFin).execute({
    accountId,
    zoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    validId: networkConfig[network].VALID_ID,
    accountStatus: 'applying',
  })

  // Biz側で同期実行
  await new SyncAccountTask(Network.LocalBiz).execute({ accountId })
  await delay()

  // BizZoneアカウントのアクティブ化
  await new SetActiveBusinessAccountWithZoneTask(network).execute({ accountId })
  await delay()

  // アカウントをアクティブ化
  await new SetAccountStatusTask(network).execute({
    accountId,
    accountStatus: commonConfig.STATUS_ACTIVE,
  })
  await delay()

  // Charge制限超過テスト
  const chargeCheckResult = await new CheckExchangeTask(network).execute({
    accountId,
    amount: '200',
    fromZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    toZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
  })
  resultManager.recordErrorCodeTest('Charge Limit Error', ERR.FINACCOUNT.EXCEEDED_CHARGE_LIMIT, chargeCheckResult)

  // Charge tokens to Biz zone
  await new TransferTask(Network.LocalFin).execute({
    accountId: accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    amount: '100',
  })
  await delay()

  // DisCharge制限超過テスト
  const disChargeCheckResult = await new CheckExchangeTask(network).execute({
    accountId,
    amount: '70',
    fromZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
    toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
  })
  resultManager.recordErrorCodeTest(
    'DisCharge Limit Error',
    ERR.FINACCOUNT.EXCEEDED_DISCHARGE_LIMIT,
    disChargeCheckResult,
  )
}

async function testFinancialDailyLimitErrors(
  network: Network,
  accountId: string,
  toAccountId: string,
  resultManager: TestResultManager,
): Promise<void> {
  console.info('Testing Financial Zone Account daily limit errors...')

  // 制限値を設定
  await new ModTokenLimitTask(network).execute({
    accountId,
    limitValues: JSON.stringify(DAILY_STRICT_LIMIT_VALUES),
  })

  // Mint制限超過テスト
  const mintCheckResult = await new CheckMintTask(network).execute({ accountId, amount: '1050' })
  resultManager.recordErrorCodeTest('Daily Mint Limit Error', ERR.FINACCOUNT.EXCEEDED_DAILY_MINT_LIMIT, mintCheckResult)

  // Burn制限超過テスト
  const burnCheckResult = await new CheckBurnTask(network).execute({ accountId, amount: '1050' })
  resultManager.recordErrorCodeTest('Daily Burn Limit Error', ERR.FINACCOUNT.EXCEEDED_DAILY_BURN_LIMIT, burnCheckResult)

  // 十分な残高を設定
  await new MintTokenTask(network).execute({ accountId, amount: '1000' })
  await delay()

  // Transfer制限超過テスト
  const transferCheckResult = await new CheckTransactionTask(network).execute({
    sendAccountId: accountId,
    fromAccountId: accountId,
    toAccountId: toAccountId,
    amount: '200',
  })
  resultManager.recordErrorCodeTest(
    'Daily Transfer Limit Error',
    ERR.FINACCOUNT.EXCEEDED_DAILY_TRANSFER_LIMIT,
    transferCheckResult,
  )

  // Fin側で同期申請（applying）
  await new CheckSyncAccountTask(Network.LocalFin).execute({
    accountId,
    zoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    validId: networkConfig[network].VALID_ID,
    accountStatus: 'applying',
  })

  // Biz側で同期実行
  await new SyncAccountTask(Network.LocalBiz).execute({ accountId })
  await delay()

  // BizZoneアカウントのアクティブ化
  await new SetActiveBusinessAccountWithZoneTask(network).execute({ accountId })
  await delay()

  // アカウントをアクティブ化
  await new SetAccountStatusTask(network).execute({
    accountId,
    accountStatus: commonConfig.STATUS_ACTIVE,
  })
  await delay()

  // // Charge制限超過テスト
  const chargeCheckResult = await new CheckExchangeTask(network).execute({
    accountId,
    amount: '600',
    fromZoneId: networkConfig[Network.LocalFin].ZONE_ID,
    toZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
  })
  resultManager.recordErrorCodeTest(
    'Daily Charge Limit Error',
    ERR.FINACCOUNT.EXCEEDED_DAILY_CHARGE_LIMIT,
    chargeCheckResult,
  )

  // Charge tokens to Biz zone
  await new TransferTask(Network.LocalFin).execute({
    accountId: accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    amount: '500',
  })
  await delay()

  // DisCharge制限超過テスト
  const disChargeCheckResult = await new CheckExchangeTask(network).execute({
    accountId,
    amount: '450',
    fromZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
    toZoneId: networkConfig[Network.LocalFin].ZONE_ID,
  })
  resultManager.recordErrorCodeTest(
    'Daily DisCharge Limit Error',
    ERR.FINACCOUNT.EXCEEDED_DAILY_DISCHARGE_LIMIT,
    disChargeCheckResult,
  )

  // General制限超過テスト
  const generalCheckResult = await new CheckMintTask(network).execute({ accountId, amount: '10' })
  resultManager.recordErrorCodeTest(
    'Daily General Limit Error',
    ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT,
    generalCheckResult,
  )
}
