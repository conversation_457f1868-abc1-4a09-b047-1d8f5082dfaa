// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/IProviderStorage.sol";
import "./interfaces/Error.sol";
import "./remigration/RemigrationLib.sol";
import {ProviderData, ZoneData, ProviderAll} from "./interfaces/Struct.sol";

/**
 * @dev ProviderStorageコントラクト
 *      Providerデータのストレージ操作を定義
 *      ProviderLogicコントラクトからのみ呼び出し可能
 */
contract ProviderStorage is Initializable, IProviderStorage {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev ProviderLogicアドレス */
    address private _providerLogicAddr;

    /** @dev プロバイダID */
    bytes32 private _providerId;

    /** @dev ゾーンIDリスト */
    uint16[] private _zoneIds;

    /** @dev プロバイダデータ */
    mapping(bytes32 => ProviderData) private _providerData;

    /** @dev ゾーンデータ */
    mapping(uint16 => ZoneData) private _zoneData;

    /* @dev setProviderAllのsignature検証用 */
    string private constant SET_PROVIDER_ALL_SIGNATURE = "setProviderAll";

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    modifier onlyProviderLogic() {
        require(msg.sender == _providerLogicAddr, Error.RV0003_PROV_INVALID_VAL);
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * @param providerLogicAddr ProviderLogicコントラクトアドレス
     */
    function initialize(IContractManager contractManager, address providerLogicAddr)
        public
        initializer
    {
        require(
            address(contractManager) != address(0) && providerLogicAddr != address(0),
            Error.RV0003_PROV_INVALID_VAL
        );
        _contractManager = contractManager;
        _providerLogicAddr = providerLogicAddr;
    }

    /**
     * @dev コントラクトバージョン取得
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // ProviderData CRUD操作
    ///////////////////////////////////

    /**
     * @dev プロバイダデータを取得する
     * @param providerId プロバイダID
     * @return providerData プロバイダデータ
     */
    function getProviderData(bytes32 providerId)
        external
        view
        override
        returns (ProviderData memory providerData)
    {
        return _providerData[providerId];
    }

    /**
     * @dev プロバイダデータを設定する
     * @param providerId プロバイダID
     * @param providerData プロバイダデータ
     */
    function setProviderData(bytes32 providerId, ProviderData memory providerData)
        external
        override
        onlyProviderLogic
    {
        _providerData[providerId] = providerData;
    }

    /**
     * @dev プロバイダの名前を更新する
     * @param providerId プロバイダID
     * @param name 新しい名前
     */
    function updateProviderName(bytes32 providerId, bytes32 name)
        external
        override
        onlyProviderLogic
    {
        _providerData[providerId].name = name;
    }

    /**
     * @dev ZoneIDを取得する。
     * @return zoneId 領域ID (デジタル通貨区分)
     * @return zoneName 領域名 (デジタル通貨区分)
     * @return err bytes(err).length!=0の場合、エラー有り(その他の戻り値は無効)
     */
    function getZone()
        external
        view
        returns (
            uint16 zoneId,
            string memory zoneName,
            string memory err
        )
    {
        if (_providerId == Constant.EMPTY_BYTES32) {
            return (Constant.EMPTY_UINT16, "", Error.GE0102_PROV_NOT_EXIST);
        }
        if (_providerData[_providerId].zoneId == Constant.EMPTY_UINT16) {
            return (Constant.EMPTY_UINT16, "", Error.GE0103_ZONE_NOT_EXIST);
        }
        return (
            _providerData[_providerId].zoneId,
            _zoneData[_providerData[_providerId].zoneId].zoneName,
            ""
        );
    }

    /**
     * @dev Zone情報を設定する
     * @param zoneId 領域ID
     * @param zoneName 領域名
     */
    function setZone(uint16 zoneId, string memory zoneName) external override onlyProviderLogic {
        _zoneIds.push(zoneId);
        _zoneData[zoneId].zoneId = zoneId;
        _zoneData[zoneId].zoneName = zoneName;
    }

    /**
     * @dev Zone名称を取得する
     * @param zoneId 領域ID
     * @return zoneName 領域名
     */
    function getZoneName(uint16 zoneId) external view override returns (string memory zoneName) {
        return _zoneData[zoneId].zoneName;
    }

    /**
     * @dev プロバイダ数を取得する
     * @return count プロバイダ数
     */
    function getProviderCount() external view override returns (uint256 count) {
        return _providerId == Constant.EMPTY_BYTES32 ? Constant.EMPTY_LENGTH : 1;
    }

    /**
     * @dev Issuerリストを設定する
     * @param zoneId 領域ID
     * @param issuerIds IssuerIDリスト
     */
    function setAvailableIssuerIds(uint16 zoneId, bytes32[] memory issuerIds)
        external
        override
        onlyProviderLogic
    {
        _zoneData[zoneId].availableIssuerIds = issuerIds;
    }

    /**
     * @dev Issuerリストを取得する
     * @param zoneId 領域ID
     * @return issuerIds IssuerIDリスト
     */
    function getAvailableIssuerIds(uint16 zoneId)
        external
        view
        override
        returns (bytes32[] memory issuerIds)
    {
        return _zoneData[zoneId].availableIssuerIds;
    }

    ///////////////////////////////////
    // ProviderId管理
    ///////////////////////////////////

    /**
     * @dev 現在のプロバイダIDを取得する
     * @return providerId 現在のプロバイダID
     */
    function getProviderId() external view override returns (bytes32 providerId) {
        return _providerId;
    }

    /**
     * @dev 現在のプロバイダIDを設定する
     * @param providerId 設定するプロバイダID
     */
    function setProviderId(bytes32 providerId) external override onlyProviderLogic {
        _providerId = providerId;
    }

    ///////////////////////////////////
    // バックアップ・リストア用
    ///////////////////////////////////

    /**
     * @dev 全プロバイダデータを設定する（バックアップ・リストア用）
     * @param provider 全プロバイダデータ
     */
    function setProviderAll(
        ProviderAll memory provider,
        uint256 deadline,
        bytes memory signature
    ) external override onlyProviderLogic {
        _providerId = provider.providerId;
        RemigrationLib.setProviderAll(
            _providerData[provider.providerId],
            _zoneIds,
            _zoneData,
            address(_contractManager),
            provider
        );
    }

    /**
     * @dev 全プロバイダデータを取得する（バックアップ・リストア用）
     * @return provider 全プロバイダデータ
     */
    function getProviderAll(bytes32 providerId)
        external
        view
        override
        returns (ProviderAll memory provider)
    {
        provider = RemigrationLib.getProviderAll(
            _providerData[providerId],
            _zoneIds,
            _zoneData,
            address(_contractManager),
            _providerId
        );
        return provider;
    }

    /**
     * @dev ProviderLogicアドレスを更新する（Admin権限必要）
     * @param providerLogicAddr 新しいProviderLogicアドレス
     */
    function setProviderLogicAddress(address providerLogicAddr) external {
        require(providerLogicAddr != address(0), Error.RV0003_PROV_INVALID_VAL);
        _providerLogicAddr = providerLogicAddr;
    }

    /**
     * @dev ProviderLogicアドレスを取得する
     * @return providerLogicAddr ProviderLogicアドレス
     */
    function getProviderLogicAddress() external view returns (address) {
        return _providerLogicAddr;
    }

    /**
     * @dev ContractManagerアドレスを更新する（将来の拡張用）
     * @param contractManagerAddr 新しいContractManagerアドレス
     */
    function setContractManagerAddress(address contractManagerAddr) external {
        require(contractManagerAddr != address(0), Error.RV0003_PROV_INVALID_VAL);
        _contractManager = IContractManager(contractManagerAddr);
    }

    /**
     * @dev ContractManagerアドレスを取得する
     * @return contractManagerAddr ContractManagerアドレス
     */
    function getContractManagerAddress() external view override returns (address) {
        return address(_contractManager);
    }
}
