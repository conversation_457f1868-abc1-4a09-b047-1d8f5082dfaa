// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";
import "../interfaces/IAccountStorage.sol";
import {ContractManager} from "../ContractManager.sol";

/**
 * @dev AccountLogicCallLibライブラリ
 *      Accountのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */

library AccountLogicCallLib {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev バリデーション用のステータス値(アクティブ) */
    bytes32 private constant STATUS_ACTIVE = "active";
    /** @dev バリデーション用のステータス値(凍結) */
    bytes32 private constant STATUS_FROZEN = "frozen";
    /** @dev バリデーション用のステータス値(解約済) */
    bytes32 private constant STATUS_TERMINATED = "terminated";
    /** @dev バリデーション用のステータス値(強制償却済) */
    bytes32 private constant STATUS_FORCE_BURNED = "force_burned";
    /** @dev 未登録の場合にて返す空の値 */
    bytes32 private constant EMPTY_BYTES32 = 0x00;

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Tokenコントラクトからの呼び出しである事が条件。
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkSenderIsToken(IContractManager contractManager) internal view {
        require(msg.sender == address(contractManager.token()), Error.GA0016_NOT_TOKEN_CONTRACT);
    }

    /**
     * @dev TokenコントラクトまたはIBCTokenコントラクトからの呼び出しである事が条件。
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkSenderIsTokenOrIbcToken(IContractManager contractManager) internal view {
        require(
            (msg.sender == address(contractManager.token()) ||
                msg.sender == address(contractManager.ibcToken())),
            Error.GA0016_NOT_TOKEN_CONTRACT
        );
    }

    /**
     * @dev Issuerコントラクトからの呼び出しである事が条件。
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkSenderIsIssuer(IContractManager contractManager) internal view {
        require(msg.sender == address(contractManager.issuer()), Error.GA0008_NOT_ISSUER_CONTRACT);
    }

    /**
     * @dev Account追加の検証処理
     * @param contractManager ContractManagerコントラクト参照
     * @param accountStorage AccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */

    function checkAddAccountIsValid(
        IContractManager contractManager,
        IAccountStorage accountStorage,
        bytes32 accountId
    ) internal view {
        // Validatorコントラクトからの呼び出しである事が条件
        checkSenderIsValidator(contractManager);

        // Accountの未入力チェック確認
        require(accountId != EMPTY_BYTES32, Error.GE0105_ACCOUNT_ID_NOT_EXIST);

        // ID重複確認
        require(!accountStorage.getAccountIdExistence(accountId), Error.GE1010_ACCOUNT_ID_EXIST);
    }

    /**
     * @dev Validatorコントラクトからの呼び出しである事が条件。
     * @param contractManager ContractManagerコントラクト参照
     */

    function checkSenderIsValidator(IContractManager contractManager) internal view {
        require(
            msg.sender == address(contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
    }

    /**
     * @dev ValidatorコントラクトまたはFinancialCheckコントラクトからの呼び出しである事が条件。
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkSenderIsTokenOrFinancial(IContractManager contractManager) internal view {
        require(
            msg.sender == address(contractManager.token()) ||
                msg.sender == address(contractManager.financialCheck()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
    }

    /**
     * @dev AccountRole追加の検証処理
     * @param contractManager ContractManagerコントラクト参照
     * @param accountEoa accountEoa
     */
    function checkAddAccountRoleIsValid(IContractManager contractManager, address accountEoa)
        internal
        view
    {
        // 呼び出し元のコントラクトが、issuerコントラクトからの呼び出されているか確認
        require(msg.sender == address(contractManager.issuer()), Error.GA0008_NOT_ISSUER_CONTRACT);
        /// accountEoaが有効な値であるか確認
        require(accountEoa != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);
    }

    /**
     * @dev AccountID存在確認 本体(内部関数)。
     * @param accountStorage AccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function checkHasAccount(IAccountStorage accountStorage, bytes32 accountId) external view {
        (bool success, string memory err) = hasAccount(accountStorage, accountId);
        require(success, err);
    }

    /**
     * @dev AccountID存在確認 本体(内部関数)。
     * @param accountStorage AccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function hasAccount(IAccountStorage accountStorage, bytes32 accountId)
        internal
        view
        returns (bool success, string memory err)
    {
        if (accountId == EMPTY_BYTES32) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
        if (!accountStorage.getAccountIdExistence(accountId)) {
            return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev Accountのステータス更新の検証処理
     * @param contractManager ContractManagerコントラクト参照
     * @param accountStorage AccountStorageコントラクト参照
     * @param msgSender メッセージ送信者
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     */
    function checkAccountStatusIsValid(
        IAccountStorage accountStorage,
        IContractManager contractManager,
        address msgSender,
        bytes32 accountId,
        bytes32 accountStatus
    ) internal view {
        // 呼び出し元のコントラクトが、issuerコントラクトからの呼び出されているか確認
        require(msgSender == address(contractManager.issuer()), Error.GA0008_NOT_ISSUER_CONTRACT);
        //Account存在確認
        {
            (bool success, string memory errTmp) = contractManager.account().hasAccount(accountId);
            require(success, errTmp);
        }
        bytes32 currentStatus = accountStorage.getAccountStatus(accountId);

        require(
            accountStatus == STATUS_ACTIVE || accountStatus == STATUS_FROZEN,
            Error.RV0007_ACCOUNT_INVALID_VAL
        );

        if (accountStatus == STATUS_ACTIVE) {
            require(
                (currentStatus == STATUS_FROZEN || currentStatus == STATUS_FORCE_BURNED),
                Error.GE2020_ACCOUNT_NOT_FROZEN_OR_FORCE_BURNED
            );
        }
        if (accountStatus == STATUS_FROZEN) {
            require(currentStatus == STATUS_ACTIVE, Error.GE2005_ACCOUNT_DISABLED);
        }
    }

    /**
     * @dev Accountの解約の検証処理
     * @param contractManager ContractManagerコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function checkTerminatedIsValid(IContractManager contractManager, bytes32 accountId)
        external
        view
    {
        // 呼び出し元のコントラクトのmsg.senderがvalidatorコントラクトからの呼び出しである事が条件
        checkSenderIsValidator(contractManager);

        //Account存在確認
        (bool success, string memory err) = contractManager.account().hasAccount(accountId);
        require(success, err);

        // Accountの残高を確認
        (AccountDataWithoutZoneId memory accountData, ) = contractManager.account().getAccount(
            accountId
        );
        require(accountData.balance == 0, Error.GE2017_ACCOUNT_BALANCE_NOT_ZERO);
    }

    /**
     * @dev 全ゾーンの残高を取得する
     * @param contractManager ContractManagerコントラクト参照
     * @param accountStorage AccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @return allBalance 全ゾーンの残高
     * @return totalBalance 合計残高
     */
    function getAllBalance(
        IAccountStorage accountStorage,
        IContractManager contractManager,
        bytes32 accountId
    ) public view returns (AllBalanceData[] memory allBalance, uint256 totalBalance) {
        // ゾーンの取得
        (uint16 zoneId, , ) = contractManager.provider().getZone();
        // ゾーンの取得
        ZoneData[] memory zones = contractManager.account().getZoneByAccountId(accountId);

        // 配列の初期化（FinZone + BizZones）
        allBalance = new AllBalanceData[](zones.length + 1);

        // FinZoneの残高
        allBalance[0].zoneId = zoneId;
        allBalance[0].balance = accountStorage.getAccountBalance(accountId);
        totalBalance = accountStorage.getAccountBalance(accountId);

        // BizZoneデータを昇順で配置（3001-3999の範囲）
        uint256 insertIndex = 1;
        for (uint16 targetZoneId = 3001; targetZoneId <= 3999; targetZoneId++) {
            for (uint256 i = 0; i < zones.length; i++) {
                if (zones[i].zoneId == targetZoneId) {
                    allBalance[insertIndex].zoneId = targetZoneId;
                    BusinessZoneAccountData memory bizData = contractManager
                        .businessZoneAccount()
                        .getBusinessZoneAccount(targetZoneId, accountId);
                    allBalance[insertIndex].balance = bizData.balance;
                    insertIndex++;
                    totalBalance += bizData.balance;
                    break;
                }
            }
            if (insertIndex > zones.length) break;
        }

        return (allBalance, totalBalance);
    }

    /**
     * @dev アカウントに紐づくバリデータIDを取得する
     * @param accountStorage AccountStorage参照
     * @param accountId マッピングのキーとなるアカウントID
     * @return validatorId バリデータID
     * @return err エラー
     */
    function getValidatorIdByAccountId(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (bytes32 validatorId, string memory err)
    {
        bool success;
        (success, err) = hasAccount(accountStorage, accountId);
        if (!success) {
            return (EMPTY_BYTES32, err);
        }

        return (accountStorage.getValidatorId(accountId), "");
    }

    /**
     * @dev アカウントの残高を取得する
     * @param accountStorage AccountStorage参照
     * @param accountId マッピングのキーとなるアカウントID
     * @return balance アカウントの残高
     */
    function getAccountBalance(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (uint256 balance)
    {
        return accountStorage.getAccountBalance(accountId);
    }

    /**
     * @dev アカウントがアクティブかどうかを取得する
     *
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @return success
     * @return err
     */
    function isActivated(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (bool success, string memory err)
    {
        if (accountStorage.getAccountStatus(accountId) == STATUS_ACTIVE) {
            return (true, "");
        } else {
            return (false, Error.GE2005_ACCOUNT_DISABLED);
        }
    }

    /**
     * @dev アカウントが解約状態かどうかを取得する
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @return terminated
     */
    function isTerminated(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (bool terminated)
    {
        if (accountStorage.getAccountStatus(accountId) == STATUS_TERMINATED) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @dev Accountの情報を返す。
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @param success アカウント存在確認結果
     * @param errTmp アカウント存在確認エラーメッセージ
     * @return accountDataWithoutZoneId アカウントデータ
     * @return err エラーメッセージ
     */
    function getAccountDataWithoutZoneId(
        IAccountStorage accountStorage,
        bytes32 accountId,
        bool success,
        string memory errTmp
    )
        external
        view
        returns (AccountDataWithoutZoneId memory accountDataWithoutZoneId, string memory err)
    {
        if (!success) {
            return (accountDataWithoutZoneId, errTmp);
        }
        accountDataWithoutZoneId = accountStorage.getAccountDataWithoutZoneId(accountId);
        return (accountDataWithoutZoneId, "");
    }

    /**
     * @dev Accountの全情報を返す。
     * @param accountStorage AccountStorage 契約リファレンス
     * @param contractManager ContractManagerコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountDataAll アカウントデータ
     */
    function getAccountDataAll(
        IAccountStorage accountStorage,
        IContractManager contractManager,
        bytes32 accountId
    ) external view returns (AccountDataAll memory accountDataAll) {
        // アカウントデータとフィナンシャルゾーンアカウントデータの取得
        AccountData memory accountData = accountStorage.getAccountData(accountId);
        // フィナンシャルゾーンアカウントデータの取得
        (FinancialZoneAccountData memory financialZoneAccountData, ) = contractManager
            .financialZoneAccount()
            .getAccountLimitData(accountId);
        // ゾーン情報の取得 TODO: エラーリターンの考慮をする
        (uint16 zoneId, string memory zoneName, ) = contractManager.provider().getZone();

        // ビジネスゾーンアカウントデータの準備
        // TODO: ループが無制限になることを防ぐため、zoneIdsの取得上限に1000件などのlimitを設ける
        uint16[] memory zoneIds = accountData.zoneIds;
        BusinessZoneAccountDataWithZoneId[]
            memory businessZoneAccountDataWithZoneIds = new BusinessZoneAccountDataWithZoneId[](
                zoneIds.length
            );
        // 各ビジネスゾーンアカウントデータの取得
        for (uint256 i = 0; i < zoneIds.length; i++) {
            BusinessZoneAccountData memory bizZoneData = contractManager
                .businessZoneAccount()
                .getBusinessZoneAccount(zoneIds[i], accountId);
            businessZoneAccountDataWithZoneIds[i] = _packZoneAccountData(
                contractManager,
                zoneIds[i],
                bizZoneData
            );
        }
        return
            AccountDataAll({
                accountName: accountData.accountName,
                accountStatus: accountData.accountStatus,
                balance: accountData.balance,
                reasonCode: accountData.reasonCode,
                zoneId: zoneId,
                zoneName: zoneName,
                appliedAt: accountData.appliedAt,
                registeredAt: accountData.registeredAt,
                terminatingAt: accountData.terminatingAt,
                terminatedAt: accountData.terminatedAt,
                mintLimit: financialZoneAccountData.mintLimit,
                burnLimit: financialZoneAccountData.burnLimit,
                chargeLimit: financialZoneAccountData.chargeLimit,
                dischargeLimit: financialZoneAccountData.dischargeLimit,
                transferLimit: financialZoneAccountData.transferLimit,
                cumulativeLimit: financialZoneAccountData.cumulativeLimit,
                cumulativeAmount: financialZoneAccountData.cumulativeAmount,
                cumulativeDate: financialZoneAccountData.cumulativeDate,
                cumulativeTransactionLimits: financialZoneAccountData.cumulativeTransactionLimits,
                businessZoneAccounts: businessZoneAccountDataWithZoneIds
            });
    }

    /**
     * @dev BusinessZoneAccountDataをBusinessZoneAccountDataWithZoneIdに変換する
     * @param contractManager ContractManagerコントラクト参照
     * @param zoneId zoneId
     * @param data BusinessZoneAccountData
     * @return BusinessZoneAccountDataWithZoneId
     */
    function _packZoneAccountData(
        IContractManager contractManager,
        uint16 zoneId,
        BusinessZoneAccountData memory data
    ) private view returns (BusinessZoneAccountDataWithZoneId memory) {
        return
            BusinessZoneAccountDataWithZoneId({
                accountName: data.accountName,
                zoneId: zoneId,
                zoneName: contractManager.provider().getZoneName(zoneId),
                balance: data.balance,
                accountStatus: data.accountStatus,
                appliedAt: data.appliedAt,
                registeredAt: data.registeredAt,
                terminatingAt: data.terminatingAt,
                terminatedAt: data.terminatedAt
            });
    }

    /**
     * @dev IndexよりAccountIDを取得する。
     *
     * @param accountStorage AccountStorage 契約リファレンス
     * @param index index
     * @return accountId accountId
     * @return err エラーメッセージ
     */
    function getAccountId(IAccountStorage accountStorage, uint256 index)
        external
        view
        returns (bytes32 accountId, string memory err)
    {
        return accountStorage.getAccountId(index);
    }

    /**
     * @dev アカウントの許可額を取得する
     *
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @param index 許可対象のアカウントID
     * @return allowance
     * @return approvedAt
     */
    function getAllowance(
        IAccountStorage accountStorage,
        bytes32 accountId,
        bytes32 index
    ) external view returns (uint256 allowance, uint256 approvedAt) {
        return accountStorage.getAllowance(accountId, index);
    }

    /**
     * @dev 送金許可一覧照会 TODO:Core APIとのマッピング時に作成
     * @param accountStorage AccountStorage 契約リファレンス
     * @param ownerId 送金許可元ID
     * @param offset オフセット
     * @param limit リミット
     * @return approvalData 送金許可設定一覧
     * @return totalCount 総数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        IAccountStorage accountStorage,
        bytes32 ownerId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        )
    {
        return accountStorage.getAllowanceList(ownerId, offset, limit);
    }

    /**
     * @dev Accountの数を返却する。
     * @param accountStorage AccountStorage 契約リファレンス
     * @return count accountの数
     */
    function getAccountCount(IAccountStorage accountStorage) external view returns (uint256 count) {
        return accountStorage.getAccountCount();
    }

    /**
     * @dev アカウントに連携済みのzoneIdの取得
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @return zoneIdList アカウントに連携済みのzoneIdのリスト
     */
    function getAccountZoneIdList(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (uint16[] memory zoneIdList)
    {
        return accountStorage.getAccountZoneIdList(accountId);
    }

    /**
     * @dev Accountが凍結状態かどうかを取得する
     *
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @return frozen
     */
    function isFrozen(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (bool frozen)
    {
        if (accountStorage.getAccountStatus(accountId) == STATUS_FROZEN) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @dev indexよりaccount詳細情報を取得する
     *
     * @param accountStorage AccountStorage 契約リファレンス
     * @param index マッピングのキーとなるアカウントID
     * @return account アカウント全ての情報
     */
    function getAccountAll(IAccountStorage accountStorage, uint256 index)
        external
        view
        returns (AccountsAll memory account)
    {
        return accountStorage.getAccountAll(index);
    }
}
