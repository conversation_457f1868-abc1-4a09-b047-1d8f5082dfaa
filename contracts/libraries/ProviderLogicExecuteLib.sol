// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IProviderStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev ProviderLogicExecuteLibライブラリ
 *      Providerの実行関数を実装するヘルパーライブラリ
 */
library ProviderLogicExecuteLib {
    /** @dev Providerロール計算用(calcRole()のprefix用文字列(Provider権限)) */
    bytes32 public constant ROLE_PREFIX_PROV = keccak256("PROV_ROLE");

    /**
     * @dev Providerの追加処理を実行する
     *
     * @param providerStorage ProviderStorage参照
     * @param contractManager ContractManager参照
     * @param providerId プロバイダID
     * @param zoneId ゾーンID
     * @param zoneName ゾーン名称
     */
    function executeAddProvider(
        IProviderStorage providerStorage,
        IContractManager contractManager,
        bytes32 providerId,
        uint16 zoneId,
        string memory zoneName
    ) external {
        providerStorage.setProviderId(providerId);
        providerStorage.setZone(zoneId, zoneName);
        ProviderData memory providerData = ProviderData({
            role: contractManager.accessCtrl().calcRole(ROLE_PREFIX_PROV, providerId),
            name: bytes32(0),
            zoneId: zoneId,
            enabled: true
        });
        providerStorage.setProviderData(providerId, providerData);
    }

    /**
     * @dev ゾーン情報の追加処理を実行する
     *
     * @param providerStorage ProviderStorage参照
     * @param zoneId ゾーンID
     * @param zoneName ゾーン名称
     */
    function executeAddBizZone(
        IProviderStorage providerStorage,
        uint16 zoneId,
        string memory zoneName
    ) external {
        providerStorage.setZone(zoneId, zoneName);
    }

    /**
     * @dev プロバイダ名の更新処理を実行する
     *
     * @param providerStorage ProviderStorage参照
     * @param providerId プロバイダID
     * @param name プロバイダ名
     */
    function updateProviderName(
        IProviderStorage providerStorage,
        bytes32 providerId,
        bytes32 name
    ) external {
        providerStorage.updateProviderName(providerId, name);
    }

    /**
     * @dev ゾーン名の更新処理を実行する
     *
     * @param providerStorage ProviderStorage参照
     * @param providerId プロバイダID
     * @param zoneName ゾーン名称
     */
    function updateZoneName(
        IProviderStorage providerStorage,
        bytes32 providerId,
        string memory zoneName
    ) external {
        ProviderData memory providerData = providerStorage.getProviderData(providerId);
        providerStorage.setZone(providerData.zoneId, zoneName);
    }

    /**
     * @dev バックアップ用に全プロバイダデータを設定する
     *
     * @param providerStorage ProviderStorage参照
     * @param provider 全プロバイダデータ
     * @param deadline 署名の期限
     * @param signature Admin署名
     */
    function setProviderAll(
        IProviderStorage providerStorage,
        ProviderAll memory provider,
        uint256 deadline,
        bytes memory signature
    ) external {
        providerStorage.setProviderAll(provider, deadline, signature);
    }

    /**
     * @dev ゾーンとIssuerの紐付け処理を実行する
     *
     * @param providerStorage ProviderStorage参照
     * @param zoneId ゾーンID
     * @param issuerId 発行者ID
     */
    function executeAddBizZoneToIssuer(
        IProviderStorage providerStorage,
        uint16 zoneId,
        bytes32 issuerId
    ) external {
        bytes32[] memory issuerIds = providerStorage.getAvailableIssuerIds(zoneId);
        bytes32[] memory newIssuerIds = new bytes32[](issuerIds.length + 1);
        for (uint256 i = 0; i < issuerIds.length; i++) {
            newIssuerIds[i] = issuerIds[i];
        }
        newIssuerIds[issuerIds.length] = issuerId;
        providerStorage.setAvailableIssuerIds(zoneId, newIssuerIds);
    }

    /**
     * @dev ゾーンとIssuerの紐付け削除処理を実行する
     *
     * @param providerStorage ProviderStorage参照
     * @param zoneId ゾーンID
     * @param issuerId 発行者ID
     */
    function executeDeleteBizZoneToIssuer(
        IProviderStorage providerStorage,
        uint16 zoneId,
        bytes32 issuerId
    ) external {
        bytes32[] memory issuerIds = providerStorage.getAvailableIssuerIds(zoneId);
        uint256 length = issuerIds.length;
        bytes32[] memory newIssuerIds = new bytes32[](length - 1);
        uint256 index = 0;
        bool found = false;
        for (uint256 i = 0; i < length; i++) {
            if (issuerIds[i] == issuerId) {
                found = true;
                continue;
            }
            newIssuerIds[index] = issuerIds[i];
            index++;
        }
        require(found, "Issuer not found");
        providerStorage.setAvailableIssuerIds(zoneId, newIssuerIds);
    }
}
