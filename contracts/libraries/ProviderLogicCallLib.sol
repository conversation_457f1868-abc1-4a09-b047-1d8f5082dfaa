// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IProviderStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";
import {ContractManager} from "../ContractManager.sol";

/**
 * @dev ProviderLogicCallLibライブラリ
 *      Providerのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */
library ProviderLogicCallLib {
    ///////////////////////////////////
    // Validation Functions
    ///////////////////////////////////

    /**
     * @dev ProviderRole追加の有効性チェック
     * @param providerStorage ProviderStorageコントラクト参照
     * @param providerId プロバイダID
     * @param providerEoa プロバイダEOA
     */
    function checkAddProviderRoleIsValid(
        IProviderStorage providerStorage,
        bytes32 providerId,
        address providerEoa
    ) external view {
        (bool success, string memory err) = hasProvider(providerStorage, providerId);
        require(success, err);
        require(providerEoa != address(0), Error.RV0003_PROV_INVALID_VAL);
    }

    /**
     * @dev AddProvider時のproviderIdチェック
     * @param providerStorage ProviderStorageコントラクト参照
     * @param providerId プロバイダID
     */
    function checkAddProviderIsValid(IProviderStorage providerStorage, bytes32 providerId)
        external
        view
    {
        // providerIdが一つしか登録できないため、providerIdが存在しないことをチェックする
        require(
            providerStorage.getProviderId() == Constant.EMPTY_BYTES32,
            Error.GE1001_PROV_ID_EXIST
        );
        // providerIdの有効性チェック
        require(providerId != Constant.EMPTY_BYTES32, Error.RV0003_PROV_INVALID_VAL);
    }

    /**
     * @dev Provider存在チェック
     * @param providerStorage ProviderStorageコントラクト参照
     */
    function validateProviderExists(IProviderStorage providerStorage) internal view {
        require(
            providerStorage.getProviderId() != Constant.EMPTY_BYTES32,
            Error.GE0101_PROV_ID_NOT_EXIST
        );
    }

    /**
     * @dev Provider情報取得
     * @param providerStorage ProviderStorageコントラクト参照
     * @return providerId プロバイダーID
     * @return zoneId ゾーンID
     * @return zoneName ゾーン名
     * @return err エラーメッセージ
     */
    function getProviderInfo(IProviderStorage providerStorage)
        internal
        view
        returns (
            bytes32 providerId,
            uint16 zoneId,
            string memory zoneName,
            string memory err
        )
    {
        providerId = providerStorage.getProviderId();
        if (providerId == Constant.EMPTY_BYTES32) {
            return (Constant.EMPTY_BYTES32, Constant.EMPTY_UINT16, "", Error.GE0102_PROV_NOT_EXIST);
        }

        ProviderData memory providerData = providerStorage.getProviderData(providerId);
        zoneId = providerData.zoneId;
        zoneName = providerStorage.getZoneName(zoneId);

        return (providerId, zoneId, zoneName, "");
    }

    /**
     * @dev providerの存在チェック
     * @param providerStorage ProviderStorageコントラクト参照
     * @param providerId プロバイダID
     */
    function checkHasProvider(IProviderStorage providerStorage, bytes32 providerId) external view {
        (bool success, string memory err) = hasProvider(providerStorage, providerId);
        require(success, err);
    }

    /**
     * @dev addTokenの検証処理
     * @param contractManager ContractManagerコントラクト参照
     * @param providerStorage ProviderStorageコントラクト参照
     * @param providerId プロバイダID
     * @param tokenId トークンID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function checkAddTokenIsValid(
        IContractManager contractManager,
        IProviderStorage providerStorage,
        bytes32 providerId,
        bytes32 tokenId,
        uint256 deadline,
        bytes memory signature,
        bytes32 name,
        bytes32 symbol
    ) external view {
        require(providerId == providerStorage.getProviderId(), Error.GE2002_NOT_PROVIDER_ID);
        ProviderData memory providerData = providerStorage.getProviderData(providerId);
        (bool has, string memory err) = contractManager.accessCtrl().checkRole(
            providerData.role,
            keccak256(abi.encode(providerId, tokenId, name, symbol, deadline)),
            deadline,
            signature
        );
        require(bytes(err).length == Constant.EMPTY_LENGTH, err);
        require(has, Error.GA0004_PROV_NOT_ROLE);
    }

    /**
     * @dev modTokenの検証処理
     * @param contractManager ContractManagerコントラクト参照
     * @param providerStorage ProviderStorageコントラクト参照
     * @param tokenId トークンID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function checkModTokenIsValid(
        IContractManager contractManager,
        IProviderStorage providerStorage,
        bytes32 tokenId,
        uint256 deadline,
        bytes memory signature,
        bytes32 name,
        bytes32 symbol
    ) external view {
        (bool success, string memory err) = contractManager.token().hasToken(tokenId, true);
        require(success, err);
        ProviderData memory providerData = providerStorage.getProviderData(
            providerStorage.getProviderId()
        );
        (bool has, string memory roleErr) = contractManager.accessCtrl().checkRole(
            providerData.role,
            keccak256(abi.encode(tokenId, name, symbol, deadline)),
            deadline,
            signature
        );
        require(bytes(roleErr).length == Constant.EMPTY_LENGTH, roleErr);
        require(has, Error.GA0004_PROV_NOT_ROLE);
    }

    /**
     * @dev addBizZoneToIssuerの検証処理
     * @param providerStorage ProviderStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param zoneId ゾーンID
     */
    function checkAddBizZoneToIssuerIsValid(
        IProviderStorage providerStorage,
        bytes32 issuerId,
        uint16 zoneId
    ) external view {
        require(
            providerStorage.getProviderId() != Constant.EMPTY_BYTES32,
            Error.GE0101_PROV_ID_NOT_EXIST
        );
        string memory zoneName = providerStorage.getZoneName(zoneId);
        require(bytes(zoneName).length > Constant.EMPTY_LENGTH, Error.GE0103_ZONE_NOT_EXIST);
        bytes32[] memory issuerIds = providerStorage.getAvailableIssuerIds(zoneId);
        // issuerId重複チェック
        for (uint256 i = 0; i < issuerIds.length; i++) {
            require(issuerIds[i] != issuerId, Error.GE1004_ISSUER_ID_EXIST);
        }
    }

    /**
     * @dev deleteBizZoneToIssuerの検証処理
     * @param providerStorage ProviderStorageコントラクト参照
     * @param zoneId ゾーンID
     */
    function checkDeleteBizZoneToIssuerIsValid(IProviderStorage providerStorage, uint16 zoneId)
        external
        view
    {
        require(
            providerStorage.getProviderId() != Constant.EMPTY_BYTES32,
            Error.GE0101_PROV_ID_NOT_EXIST
        );
        string memory zoneName = providerStorage.getZoneName(zoneId);
        require(bytes(zoneName).length > Constant.EMPTY_LENGTH, Error.GE0103_ZONE_NOT_EXIST);
    }

    /**
     * @dev プロバイダの存在確認
     * @param providerStorage ProviderStorageコントラクト参照
     * @param providerId チェック対象となるプロバイダID
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function hasProvider(IProviderStorage providerStorage, bytes32 providerId)
        public
        view
        returns (bool success, string memory err)
    {
        if (providerId == Constant.EMPTY_BYTES32) {
            return (false, Error.RV0003_PROV_INVALID_VAL);
        }
        if (providerStorage.getProviderId() != providerId) {
            return (false, Error.GE0101_PROV_ID_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev トークンの存在確認
     * @param providerStorage ProviderStorageコントラクト参照
     * @param tokenId チェック対象となるトークンID
     * @param providerId チェック対象となるプロバイダID
     * @param checkEnabled true:有効性確認を行う,false:有効性確認を行わない
     * @return success true:トークンが存在し有効,false:トークンが存在しないまたは無効
     * @return err エラーメッセージ
     */
    function hasToken(
        IProviderStorage providerStorage,
        IContractManager contractManager,
        bytes32 tokenId,
        bytes32 providerId,
        bool checkEnabled
    ) public view returns (bool success, string memory err) {
        // ProviderIDが未入力の場合エラー
        if (providerId == Constant.EMPTY_BYTES32) {
            return (false, Error.RV0003_PROV_INVALID_VAL);
        }

        // Providerが存在しない場合エラー
        if (providerStorage.getProviderId() != providerId) {
            return (false, Error.GE0101_PROV_ID_NOT_EXIST);
        }

        // TokenIDが未入力の場合エラー
        if (tokenId == Constant.EMPTY_BYTES32) {
            return (false, Error.RV0003_PROV_INVALID_VAL);
        }

        // Tokenの有効性チェック
        (success, err) = contractManager.token().hasToken(tokenId, checkEnabled);
        if (success) {
            return (true, "");
        } else {
            return (false, err);
        }
    }

    /**
     * @dev 指定されたProviderIDにZoneが紐付いているか確認を行う
     * @param providerStorage ProviderStorageコントラクト参照
     * @param providerId プロバイダID
     * @param zoneId ゾーンID
     * @return success true:紐づいている,false:紐づいていない
     * @return err エラーメッセージ
     */
    function hasZone(
        IProviderStorage providerStorage,
        bytes32 providerId,
        uint16 zoneId
    ) public view returns (bool success, string memory err) {
        (success, err) = hasProvider(providerStorage, providerId);
        if (!success) {
            return (false, err);
        }
        string memory zoneName = providerStorage.getZoneName(zoneId);
        if (bytes(zoneName).length == Constant.EMPTY_LENGTH) {
            return (false, Error.GE0103_ZONE_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev 管理者権限チェック
     * @param contractManager ContractManagerコントラクト参照
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     * @return has true:権限あり,false:権限なし
     * @return err エラーメッセージ
     */
    function hasAdminRole(
        IContractManager contractManager,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool has, string memory err) {
        return contractManager.accessCtrl().checkAdminRole(hash, deadline, signature);
    }

    /**
     * @dev 権限チェック
     * @param contractManager ContractManagerコントラクト参照
     * @param providerStorage ProviderStorageコントラクト参照
     * @param providerId プロバイダID
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     * @return has true:権限あり,false:権限なし
     * @return err エラーメッセージ
     */
    function hasProviderRole(
        IContractManager contractManager,
        IProviderStorage providerStorage,
        bytes32 providerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool has, string memory err) {
        if (providerId == Constant.EMPTY_BYTES32) {
            return (false, Error.RV0003_PROV_INVALID_VAL);
        }
        ProviderData memory providerData = providerStorage.getProviderData(providerId);
        return contractManager.accessCtrl().checkRole(providerData.role, hash, deadline, signature);
    }

    ///////////////////////////////////
    // Storage Wrapper Functions
    ///////////////////////////////////

    /**
     * @dev プロバイダデータを取得する
     * @param providerStorage ProviderStorageコントラクト参照
     * @param providerId プロバイダID
     * @return providerData プロバイダデータ
     */
    function getProviderData(IProviderStorage providerStorage, bytes32 providerId)
        external
        view
        returns (ProviderData memory providerData)
    {
        return providerStorage.getProviderData(providerId);
    }

    /**
     * @dev プロバイダIDを取得する
     * @param providerStorage ProviderStorageコントラクト参照
     * @return providerId プロバイダID
     */
    function getProviderId(IProviderStorage providerStorage) external view returns (bytes32) {
        return providerStorage.getProviderId();
    }

    /**
     * @dev ゾーン名称を取得する
     * @param providerStorage ProviderStorageコントラクト参照
     * @param zoneId ゾーンID
     * @return zoneName ゾーン名称
     */
    function getZoneName(IProviderStorage providerStorage, uint16 zoneId)
        external
        view
        returns (string memory zoneName)
    {
        return providerStorage.getZoneName(zoneId);
    }

    /**
     * @dev ZoneIDを取得する。
     * @return zoneId 領域ID (デジタル通貨区分)
     * @return zoneName 領域名 (デジタル通貨区分)
     * @return err bytes(err).length!=0の場合、エラー有り(その他の戻り値は無効)
     */
    function getZone(IProviderStorage providerStorage)
        external
        view
        returns (
            uint16 zoneId,
            string memory zoneName,
            string memory err
        )
    {
        return providerStorage.getZone();
    }

    /**
     * @dev バックアップ用にプロバイダデータを取得する
     * @param providerStorage ProviderStorageコントラクト参照
     * @param providerId プロバイダID
     * @return provider 全プロバイダデータ
     */
    function getProviderAll(IProviderStorage providerStorage, bytes32 providerId)
        external
        view
        returns (ProviderAll memory provider)
    {
        return providerStorage.getProviderAll(providerId);
    }

    /**
     * @dev 認可イシュアを確認。
     *
     * @param zoneId zoneId
     * @param accountId accountId
     * @return success 成功したかどうか
     * @return err エラーメッセージ
     */
    function checkAvailableIssuerIds(
        IProviderStorage providerStorage,
        IContractManager contractManager,
        uint16 zoneId,
        bytes32 accountId
    ) external view returns (bool success, string memory err) {
        bytes32[] memory issuerIds = providerStorage.getAvailableIssuerIds(zoneId);
        if (issuerIds.length == Constant.EMPTY_LENGTH) {
            return (false, Error.GE0104_ISSUER_ID_NOT_EXIST);
        }
        for (uint256 i = 0; i < issuerIds.length; i++) {
            (success, err) = contractManager.issuer().hasAccount(issuerIds[i], accountId);
            if (success) {
                return (true, "");
            }
        }
        return (false, err);
    }
}
