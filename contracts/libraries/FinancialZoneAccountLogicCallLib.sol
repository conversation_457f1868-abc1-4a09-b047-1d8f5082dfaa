// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/Error.sol";
import "../interfaces/IContractManager.sol";
import "../interfaces/IFinancialZoneAccountStorage.sol";
import "../interfaces/Struct.sol";

/**
 * @dev FinancialZoneAccountCallLibライブラリ
 *      FinancialZoneAccountのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */
library FinancialZoneAccountLogicCallLib {
    /** @dev 各種限度額のチェック対象(mint) **/
    bytes32 private constant _CHECK_MINT = "checkMint";
    /** @dev 各種限度額のチェック対象(burn) **/
    bytes32 private constant _CHECK_BURN = "checkBurn";
    /** @dev 各種限度額のチェック対象(charge) **/
    bytes32 private constant _CHECK_CHARGE = "checkCharge";
    /** @dev 各種限度額のチェック対象(discharge) **/
    bytes32 private constant _CHECK_DISCHARGE = "checkDischarge";
    /** @dev 各種限度額のチェック対象(transfer) **/
    bytes32 private constant _CHECK_TRANSFER = "checkTransfer";
    /** @dev getFinAccountsAllのsignature検証用 **/
    string private constant _GET_FINACCOUNTS_ALL_SIGNATURE = "getFinAccountsAll";
    /* @dev setBizAccountsAllのsignature検証用 */
    string private constant _SET_FINACCOUNTS_ALL_SIGNATURE = "setFinAccountsAll";

    /**
     * @dev アカウントの限度額値が妥当かチェック
     *
     * @param limitValues 限度額値
     */
    function checkAddAccountLimitDataIsValid(AccountLimitValues memory limitValues) external pure {
        {
            require(limitValues.mint <= Constant.MAX_LIMIT_VALUE, Error.UE4001_EXCEEDED_MINT_LIMIT);
            require(limitValues.burn <= Constant.MAX_LIMIT_VALUE, Error.UE4002_EXCEEDED_BURN_LIMIT);
            require(
                limitValues.charge <= Constant.MAX_LIMIT_VALUE,
                Error.UE4004_EXCEEDED_CHARGE_LIMIT
            );
            require(
                limitValues.discharge <= Constant.MAX_LIMIT_VALUE,
                Error.UE4005_EXCEEDED_DISCHARGE_LIMIT
            );
            require(
                limitValues.transfer <= Constant.MAX_LIMIT_VALUE,
                Error.UE4003_EXCEEDED_TRANSFER_LIMIT
            );
            require(
                limitValues.cumulative.total <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4101_EXCEEDED_DAILY_LIMIT
            );
            require(
                limitValues.cumulative.mint <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4102_EXCEEDED_DAILY_MINT_LIMIT
            );
            require(
                limitValues.cumulative.burn <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4103_EXCEEDED_DAILY_BURN_LIMIT
            );
            require(
                limitValues.cumulative.charge <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4104_EXCEEDED_DAILY_CHARGE_LIMIT
            );
            require(
                limitValues.cumulative.discharge <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4105_EXCEEDED_DAILY_DISCHARGE_LIMIT
            );
            require(
                limitValues.cumulative.transfer <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4106_EXCEEDED_DAILY_TRANSFER_LIMIT
            );
        }
    }

    /**
     * @dev Issuerコントラクトのみから呼び出し可能
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkSenderIsIssuer(IContractManager contractManager) external view {
        require(msg.sender == address(contractManager.issuer()), Error.GA0008_NOT_ISSUER_CONTRACT);
    }

    /**
     * @dev Tokenコントラクトのみから呼び出し可能
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkSenderIsToken(IContractManager contractManager) external view {
        require(msg.sender == address(contractManager.token()), Error.GA0016_NOT_TOKEN_CONTRACT);
    }

    /**
     * @dev アカウント存在チェック
     * @param accountId マッピングのキーとなるアカウントID
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkHasAccount(IContractManager contractManager, bytes32 accountId) external view {
        {
            (bool success, string memory err) = contractManager.account().hasAccount(accountId);
            require(success, err);
        }
    }

    /**
     * @dev cumulativeDate
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeDate(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId
    ) external view returns (uint256 cumulativeDate) {
        return financialZoneAccountStorage.getCumulativeDate(accountId);
    }

    /**
     * @dev cumulativeAmount
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeAmount(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId
    ) external view returns (uint256 cumulativeAmount) {
        return financialZoneAccountStorage.getCumulativeAmount(accountId);
    }

    /**
     * @dev cumulativeAmount
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeMintAmount(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId
    ) external view returns (uint256 cumulativeMintAmount) {
        return financialZoneAccountStorage.getCumulativeMintAmount(accountId);
    }

    /**
     * @dev cumulativeBurnAmount
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeBurnAmount(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId
    ) external view returns (uint256 cumulativeBurnAmount) {
        return financialZoneAccountStorage.getCumulativeBurnAmount(accountId);
    }

    /**
     * @dev cumulativeChargeAmount
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeChargeAmount(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId
    ) external view returns (uint256 cumulativeChargeAmount) {
        return financialZoneAccountStorage.getCumulativeChargeAmount(accountId);
    }

    /**
     * @dev cumulativeDischargeAmount
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeDischargeAmount(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId
    ) external view returns (uint256 cumulativeAmount) {
        return financialZoneAccountStorage.getCumulativeDischargeAmount(accountId);
    }

    /**
     * @dev cumulativeTransferAmount
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeTransferAmount(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId
    ) external view returns (uint256 cumulativeTransferAmount) {
        return financialZoneAccountStorage.getCumulativeTransferAmount(accountId);
    }

    /**
     * @dev Admin権限を持つかチェック
     * @param contractManager IContractManagerコントラクト参照
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function checkRoleAccountAdmin(
        IContractManager contractManager,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view {
        (bool has, string memory err) = contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0029_ACCOUNT_NOT_ADMIN);
    }

    /**
     * @dev 限度額データ存在確認
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return success true:OK, false:NG
     * @return err エラーメッセージ
     */
    function hasAccount(bytes32 accountId) external pure returns (bool success, string memory err) {
        if (accountId == 0x00) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
    }

    /**
     * @dev Accountコントラクト、もしくはFinancialCheckコントラクトのみから呼び出し可能
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkSenderIsTokenOrFinancial(IContractManager contractManager) external view {
        require(
            msg.sender == address(contractManager.account()) ||
                msg.sender == address(contractManager.financialCheck()),
            Error.GA0012_NOT_ACCOUNT_CONTRACT
        );
    }

    /**
     * @dev アカウントの限度額を取得する
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountLimitData FinancialZoneAccountData構造体
     */
    function getAccountLimitData(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId
    ) external view returns (FinancialZoneAccountData memory accountLimitData) {
        return financialZoneAccountStorage.getFinancialZoneAccountData(accountId);
    }

    /**
     * @dev 1日の限度額チェック
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 金額
     * @param jstDay 現在の日付
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function _checkTransactionLimits(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId,
        uint256 amount,
        bytes32 operationType,
        uint256 jstDay
    ) internal view returns (bool success, string memory err) {
        if (amount == 0) {
            return (false, Error.RV0022_TOKEN_ZERO_AMOUNT);
        }

        FinancialZoneAccountData memory data = financialZoneAccountStorage
            .getFinancialZoneAccountData(accountId);
        CumulativeTransactionLimits memory limits = financialZoneAccountStorage
            .getCumulativeTransactionLimits(accountId);

        uint256 limit;
        uint256 dailyLimit;
        uint256 dailyAmount;
        string memory errorLimit;
        string memory errorDailyLimit;

        if (operationType == _CHECK_MINT) {
            limit = data.mintLimit;
            dailyLimit = limits.cumulativeMintLimit;
            dailyAmount = limits.cumulativeMintAmount;
            errorLimit = Error.UE4001_EXCEEDED_MINT_LIMIT;
            errorDailyLimit = Error.UE4102_EXCEEDED_DAILY_MINT_LIMIT;
        } else if (operationType == _CHECK_BURN) {
            limit = data.burnLimit;
            dailyLimit = limits.cumulativeBurnLimit;
            dailyAmount = limits.cumulativeBurnAmount;
            errorLimit = Error.UE4002_EXCEEDED_BURN_LIMIT;
            errorDailyLimit = Error.UE4103_EXCEEDED_DAILY_BURN_LIMIT;
        } else if (operationType == _CHECK_CHARGE) {
            limit = data.chargeLimit;
            dailyLimit = limits.cumulativeChargeLimit;
            dailyAmount = limits.cumulativeChargeAmount;
            errorLimit = Error.UE4004_EXCEEDED_CHARGE_LIMIT;
            errorDailyLimit = Error.UE4104_EXCEEDED_DAILY_CHARGE_LIMIT;
        } else if (operationType == _CHECK_DISCHARGE) {
            limit = data.dischargeLimit;
            dailyLimit = limits.cumulativeDischargeLimit;
            dailyAmount = limits.cumulativeDischargeAmount;
            errorLimit = Error.UE4005_EXCEEDED_DISCHARGE_LIMIT;
            errorDailyLimit = Error.UE4105_EXCEEDED_DAILY_DISCHARGE_LIMIT;
        } else if (operationType == _CHECK_TRANSFER) {
            limit = data.transferLimit;
            dailyLimit = limits.cumulativeTransferLimit;
            dailyAmount = limits.cumulativeTransferAmount;
            errorLimit = Error.UE4003_EXCEEDED_TRANSFER_LIMIT;
            errorDailyLimit = Error.UE4106_EXCEEDED_DAILY_TRANSFER_LIMIT;
        }

        if (limit != Constant.MAX_LIMIT_VALUE && limit > 0 && limit < amount) {
            return (false, errorLimit);
        }

        uint256 today = jstDay;
        bool isSameDay = data.cumulativeDate == today;

        if (!isSameDay) {
            if (
                dailyLimit != Constant.MAX_DAILY_LIMIT_VALUE &&
                dailyLimit > 0 &&
                dailyLimit < amount
            ) {
                return (false, errorDailyLimit);
            }
        } else {
            if (
                dailyLimit != Constant.MAX_DAILY_LIMIT_VALUE &&
                dailyLimit > 0 &&
                dailyLimit < amount + dailyAmount
            ) {
                return (false, errorDailyLimit);
            }
        }

        if (!isSameDay) {
            if (
                data.cumulativeLimit != Constant.MAX_DAILY_LIMIT_VALUE &&
                data.cumulativeLimit > 0 &&
                data.cumulativeLimit < amount
            ) {
                return (false, Error.UE4101_EXCEEDED_DAILY_LIMIT);
            }
        } else {
            if (
                data.cumulativeLimit != Constant.MAX_DAILY_LIMIT_VALUE &&
                data.cumulativeLimit > 0 &&
                data.cumulativeLimit < amount + data.cumulativeAmount
            ) {
                return (false, Error.UE4101_EXCEEDED_DAILY_LIMIT);
            }
        }

        return (true, "");
    }

    /**
     * @dev 1日の限度額チェック
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 金額
     * @param jstDay 現在の日付
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkTransactionLimits(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId,
        uint256 amount,
        bytes32 operationType,
        uint256 jstDay
    ) external view returns (bool success, string memory err) {
        if (amount == 0) {
            return (false, Error.RV0022_TOKEN_ZERO_AMOUNT);
        }

        FinancialZoneAccountData memory data = financialZoneAccountStorage
            .getFinancialZoneAccountData(accountId);
        CumulativeTransactionLimits memory limits = financialZoneAccountStorage
            .getCumulativeTransactionLimits(accountId);

        uint256 limit;
        uint256 dailyLimit;
        uint256 dailyAmount;
        string memory errorLimit;
        string memory errorDailyLimit;

        if (operationType == _CHECK_MINT) {
            limit = data.mintLimit;
            dailyLimit = limits.cumulativeMintLimit;
            dailyAmount = limits.cumulativeMintAmount;
            errorLimit = Error.UE4001_EXCEEDED_MINT_LIMIT;
            errorDailyLimit = Error.UE4102_EXCEEDED_DAILY_MINT_LIMIT;
        } else if (operationType == _CHECK_BURN) {
            limit = data.burnLimit;
            dailyLimit = limits.cumulativeBurnLimit;
            dailyAmount = limits.cumulativeBurnAmount;
            errorLimit = Error.UE4002_EXCEEDED_BURN_LIMIT;
            errorDailyLimit = Error.UE4103_EXCEEDED_DAILY_BURN_LIMIT;
        } else if (operationType == _CHECK_CHARGE) {
            limit = data.chargeLimit;
            dailyLimit = limits.cumulativeChargeLimit;
            dailyAmount = limits.cumulativeChargeAmount;
            errorLimit = Error.UE4004_EXCEEDED_CHARGE_LIMIT;
            errorDailyLimit = Error.UE4104_EXCEEDED_DAILY_CHARGE_LIMIT;
        } else if (operationType == _CHECK_DISCHARGE) {
            limit = data.dischargeLimit;
            dailyLimit = limits.cumulativeDischargeLimit;
            dailyAmount = limits.cumulativeDischargeAmount;
            errorLimit = Error.UE4005_EXCEEDED_DISCHARGE_LIMIT;
            errorDailyLimit = Error.UE4105_EXCEEDED_DAILY_DISCHARGE_LIMIT;
        } else if (operationType == _CHECK_TRANSFER) {
            limit = data.transferLimit;
            dailyLimit = limits.cumulativeTransferLimit;
            dailyAmount = limits.cumulativeTransferAmount;
            errorLimit = Error.UE4003_EXCEEDED_TRANSFER_LIMIT;
            errorDailyLimit = Error.UE4106_EXCEEDED_DAILY_TRANSFER_LIMIT;
        }

        if (limit != Constant.MAX_LIMIT_VALUE && limit > 0 && limit < amount) {
            return (false, errorLimit);
        }

        uint256 today = jstDay;
        bool isSameDay = data.cumulativeDate == today;

        if (!isSameDay) {
            if (
                dailyLimit != Constant.MAX_DAILY_LIMIT_VALUE &&
                dailyLimit > 0 &&
                dailyLimit < amount
            ) {
                return (false, errorDailyLimit);
            }
        } else {
            if (
                dailyLimit != Constant.MAX_DAILY_LIMIT_VALUE &&
                dailyLimit > 0 &&
                dailyLimit < amount + dailyAmount
            ) {
                return (false, errorDailyLimit);
            }
        }

        if (!isSameDay) {
            if (
                data.cumulativeLimit != Constant.MAX_DAILY_LIMIT_VALUE &&
                data.cumulativeLimit > 0 &&
                data.cumulativeLimit < amount
            ) {
                return (false, Error.UE4101_EXCEEDED_DAILY_LIMIT);
            }
        } else {
            if (
                data.cumulativeLimit != Constant.MAX_DAILY_LIMIT_VALUE &&
                data.cumulativeLimit > 0 &&
                data.cumulativeLimit < amount + data.cumulativeAmount
            ) {
                return (false, Error.UE4101_EXCEEDED_DAILY_LIMIT);
            }
        }

        return (true, "");
    }

    /**
     * @dev 発行限度額チェック
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param contractManager IContractManagerコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkMint(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        IContractManager contractManager,
        bytes32 accountId,
        uint256 amount,
        uint256 JSTDay
    ) external view returns (bool success, string memory err) {
        (success, err) = contractManager.account().isActivated(accountId);
        if (!success) {
            return (false, err);
        }

        (success, err) = _checkTransactionLimits(
            financialZoneAccountStorage,
            accountId,
            amount,
            _CHECK_MINT,
            JSTDay
        );
        if (!success) {
            return (false, err);
        }

        return (true, "");
    }

    /**
     * @dev 償却限度額チェック
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param contractManager IContractManagerコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkBurn(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        IContractManager contractManager,
        bytes32 accountId,
        uint256 amount,
        uint256 JSTDay
    ) external view returns (bool success, string memory err) {
        (success, err) = contractManager.account().isActivated(accountId);
        if (!success) {
            return (false, err);
        }

        (success, err) = _checkTransactionLimits(
            financialZoneAccountStorage,
            accountId,
            amount,
            _CHECK_BURN,
            JSTDay
        );
        if (!success) {
            return (false, err);
        }

        return (true, "");
    }

    /**
     * @dev チャージ限度額チェック
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param contractManager IContractManagerコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkCharge(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        IContractManager contractManager,
        bytes32 accountId,
        uint256 amount,
        uint256 JSTDay
    ) external view returns (bool success, string memory err) {
        (success, err) = contractManager.account().isActivated(accountId);
        if (!success) {
            return (false, err);
        }

        (success, err) = _checkTransactionLimits(
            financialZoneAccountStorage,
            accountId,
            amount,
            _CHECK_CHARGE,
            JSTDay
        );
        if (!success) {
            return (false, err);
        }

        return (true, "");
    }

    /**
     * @dev ディスチャージ限度額チェック
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param contractManager IContractManagerコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkDischarge(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        IContractManager contractManager,
        bytes32 accountId,
        uint256 amount,
        uint256 JSTDay
    ) external view returns (bool success, string memory err) {
        (success, err) = contractManager.account().isActivated(accountId);
        if (!success) {
            return (false, err);
        }

        (success, err) = _checkTransactionLimits(
            financialZoneAccountStorage,
            accountId,
            amount,
            _CHECK_DISCHARGE,
            JSTDay
        );
        if (!success) {
            return (false, err);
        }

        return (true, "");
    }

    /**
     * @dev 移転限度額チェック
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param contractManager IContractManagerコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkTransfer(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        IContractManager contractManager,
        bytes32 accountId,
        uint256 amount,
        uint256 JSTDay
    ) external view returns (bool success, string memory err) {
        (success, err) = contractManager.account().isActivated(accountId);
        if (!success) {
            return (false, err);
        }

        (success, err) = _checkTransactionLimits(
            financialZoneAccountStorage,
            accountId,
            amount,
            _CHECK_TRANSFER,
            JSTDay
        );
        if (!success) {
            return (false, err);
        }

        return (true, "");
    }

    /**
     * @dev limitとoffsetで指定したFinancialZoneAccountsを一括取得する
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param index index
     */
    function getFinAccountAll(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        uint256 index
    ) external view returns (FinancialZoneAccountsAll memory finAccounts) {
        return financialZoneAccountStorage.getFinAccountAll(index);
    }
}
