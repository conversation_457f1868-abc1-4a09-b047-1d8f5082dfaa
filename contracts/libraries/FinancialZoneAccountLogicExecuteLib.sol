// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IFinancialZoneAccountStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev FinancialZoneAccountLogicExecuteLibライブラリ
 *      FinancialZoneAccountの実行関数を実装するヘルパーライブラリ
 */
library FinancialZoneAccountLogicExecuteLib {
    /**
     * @dev アカウント限度額登録
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param limitValues アカウントの限度額値
     */
    function addAccountLimitData(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId,
        AccountLimitValues memory limitValues
    ) external {
        financialZoneAccountStorage.addAccountLimitData(accountId, limitValues);
    }

    /**
     * @dev アカウント限度額更新
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param limitUpdates アカウント限度額の更新フラグ
     * @param limitValues アカウントの限度額値
     */
    function modAccountLimitData(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId,
        AccountLimitUpdates memory limitUpdates,
        AccountLimitValues memory limitValues
    ) external returns (AccountLimitValues memory) {
        FinancialZoneAccountData memory data = financialZoneAccountStorage
            .getFinancialZoneAccountData(accountId);
        // 各フラグに応じて限度額を設定
        // 発行限度額の更新
        if (limitUpdates.mint) {
            require(limitValues.mint <= Constant.MAX_LIMIT_VALUE, Error.UE4001_EXCEEDED_MINT_LIMIT);
            data.mintLimit = limitValues.mint;
        }

        // 償却限度額の更新
        if (limitUpdates.burn) {
            require(limitValues.burn <= Constant.MAX_LIMIT_VALUE, Error.UE4002_EXCEEDED_BURN_LIMIT);
            data.burnLimit = limitValues.burn;
        }

        // チャージ限度額の更新
        if (limitUpdates.charge) {
            require(
                limitValues.charge <= Constant.MAX_LIMIT_VALUE,
                Error.UE4004_EXCEEDED_CHARGE_LIMIT
            );
            data.chargeLimit = limitValues.charge;
        }

        //  返還限度額の更新
        if (limitUpdates.discharge) {
            require(
                limitValues.discharge <= Constant.MAX_LIMIT_VALUE,
                Error.UE4005_EXCEEDED_DISCHARGE_LIMIT
            );
            data.dischargeLimit = limitValues.discharge;
        }

        //  送金限度額の更新
        if (limitUpdates.transfer) {
            require(
                limitValues.transfer <= Constant.MAX_LIMIT_VALUE,
                Error.UE4003_EXCEEDED_TRANSFER_LIMIT
            );
            data.transferLimit = limitValues.transfer;
        }

        // 一日の全取引の合計累積限度額の更新
        if (limitUpdates.cumulative.total) {
            require(
                limitValues.cumulative.total <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4101_EXCEEDED_DAILY_LIMIT
            );
            data.cumulativeLimit = limitValues.cumulative.total;
        }

        // 発行累積限度額の更新
        if (limitUpdates.cumulative.mint) {
            require(
                limitValues.cumulative.mint <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4102_EXCEEDED_DAILY_MINT_LIMIT
            );
            data.cumulativeTransactionLimits.cumulativeMintLimit = limitValues.cumulative.mint;
        }
        //償却累積限度額の更新
        if (limitUpdates.cumulative.burn) {
            require(
                limitValues.cumulative.burn <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4103_EXCEEDED_DAILY_BURN_LIMIT
            );
            data.cumulativeTransactionLimits.cumulativeBurnLimit = limitValues.cumulative.burn;
        }

        // チャージ累積限度額の更新
        if (limitUpdates.cumulative.charge) {
            require(
                limitValues.cumulative.charge <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4104_EXCEEDED_DAILY_CHARGE_LIMIT
            );
            data.cumulativeTransactionLimits.cumulativeChargeLimit = limitValues.cumulative.charge;
        }

        // 返還累積限度額の更新
        if (limitUpdates.cumulative.discharge) {
            require(
                limitValues.cumulative.discharge <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4105_EXCEEDED_DAILY_DISCHARGE_LIMIT
            );
            data.cumulativeTransactionLimits.cumulativeDischargeLimit = limitValues
                .cumulative
                .discharge;
        }

        // 移転累積限度額の更新
        if (limitUpdates.cumulative.transfer) {
            require(
                limitValues.cumulative.transfer <= Constant.MAX_DAILY_LIMIT_VALUE,
                Error.UE4106_EXCEEDED_DAILY_TRANSFER_LIMIT
            );
            data.cumulativeTransactionLimits.cumulativeTransferLimit = limitValues
                .cumulative
                .transfer;
        }
        // Event用に設定後のAccountの限度額Listを作成

        limitValues.mint = data.mintLimit;
        limitValues.burn = data.burnLimit;
        limitValues.charge = data.chargeLimit;
        limitValues.discharge = data.dischargeLimit;
        limitValues.transfer = data.transferLimit;
        limitValues.cumulative.total = data.cumulativeLimit;
        limitValues.cumulative.mint = data.cumulativeTransactionLimits.cumulativeMintLimit;
        limitValues.cumulative.burn = data.cumulativeTransactionLimits.cumulativeBurnLimit;
        limitValues.cumulative.charge = data.cumulativeTransactionLimits.cumulativeChargeLimit;
        limitValues.cumulative.discharge = data
            .cumulativeTransactionLimits
            .cumulativeDischargeLimit;
        limitValues.cumulative.transfer = data.cumulativeTransactionLimits.cumulativeTransferLimit;

        financialZoneAccountStorage.setFinancialZoneAccountData(accountId, data);

        return limitValues;
    }

    /**
     * @dev cumulative amount初期化
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param jstDay 現在の日付
     */
    function resetCumulative(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId,
        uint256 jstDay
    ) external {
        financialZoneAccountStorage.resetCumulative(accountId, jstDay);
    }

    /**
     * @dev cumulative amount初期化
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function syncCumulativeReset(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId,
        uint256 JSTDay
    ) external {
        financialZoneAccountStorage.syncCumulativeReset(accountId, JSTDay);
    }

    /**
     * @dev 累積限度額の加算を行う
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 金額
     * @param currentDay 現在の日付
     * @return cumulativeDate
     * @return cumulativeAmount
     */
    function addAmount(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId,
        uint256 amount,
        uint256 currentDay
    ) external returns (uint256 cumulativeDate, uint256 cumulativeAmount) {
        FinancialZoneAccountData memory data = financialZoneAccountStorage
            .getFinancialZoneAccountData(accountId);
        if (data.cumulativeDate == currentDay) {
            data.cumulativeAmount += amount;
        } else {
            data.cumulativeAmount = amount;
            data.cumulativeDate = currentDay;
        }
        financialZoneAccountStorage.setFinancialZoneAccountData(accountId, data);
        return (data.cumulativeDate, data.cumulativeAmount);
    }

    /**
     * @dev 累積限度額の減算を行う
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 金額
     * @return cumulativeDate
     * @return cumulativeAmount
     */
    function subtractAmount(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId,
        uint256 amount
    ) external returns (uint256 cumulativeDate, uint256 cumulativeAmount) {
        FinancialZoneAccountData memory data = financialZoneAccountStorage
            .getFinancialZoneAccountData(accountId);
        data.cumulativeAmount -= amount;
        financialZoneAccountStorage.setFinancialZoneAccountData(accountId, data);
        return (data.cumulativeDate, data.cumulativeAmount);
    }

    /**
     * @dev 累積限度額の更新を行う
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 金額
     * @param currentDay 現在の日付
     * @param operationType 取引種別（Constant.SYNC_MINT, SYNC_BURN, SYNC_CHARGE, SYNC_DISCHARGE, SYNC_TRANSFER）
     */
    function syncLimitAmount(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        bytes32 accountId,
        uint256 amount,
        uint256 currentDay,
        bytes32 operationType
    ) external {
        FinancialZoneAccountData memory data = financialZoneAccountStorage
            .getFinancialZoneAccountData(accountId);
        CumulativeTransactionLimits memory limits = financialZoneAccountStorage
            .getCumulativeTransactionLimits(accountId);

        // 累積日付が更新されるべき場合（cumulativeDate < currentDay）に累積額をリセット
        bool isNewCumulativeDay = data.cumulativeDate < currentDay;

        if (isNewCumulativeDay) {
            // 累積日付が古い場合は累積額をリセットして新しい値を設定
            data.cumulativeAmount = amount;

            // 全ての累積額をリセット
            limits.cumulativeMintAmount = Constant.RESET_CUMULATIVE_AMOUNT;
            limits.cumulativeBurnAmount = Constant.RESET_CUMULATIVE_AMOUNT;
            limits.cumulativeChargeAmount = Constant.RESET_CUMULATIVE_AMOUNT;
            limits.cumulativeDischargeAmount = Constant.RESET_CUMULATIVE_AMOUNT;
            limits.cumulativeTransferAmount = Constant.RESET_CUMULATIVE_AMOUNT;

            data.cumulativeDate = currentDay;
        } else {
            // 同じ日の場合は累積額に加算
            data.cumulativeAmount += amount;
        }

        // 取引種別に応じた累積額を更新
        if (operationType == Constant.SYNC_MINT) {
            limits.cumulativeMintAmount += amount;
        } else if (operationType == Constant.SYNC_BURN) {
            limits.cumulativeBurnAmount += amount;
        } else if (operationType == Constant.SYNC_CHARGE) {
            limits.cumulativeChargeAmount += amount;
        } else if (operationType == Constant.SYNC_DISCHARGE) {
            limits.cumulativeDischargeAmount += amount;
        } else if (operationType == Constant.SYNC_TRANSFER) {
            limits.cumulativeTransferAmount += amount;
        }

        financialZoneAccountStorage.setFinancialZoneAccountData(accountId, data);
        financialZoneAccountStorage.setCumulativeTransactionLimits(accountId, limits);
    }

    /**
     * @dev 指定されたAcountIdsに紐づくFinancialZoneAccounts情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     * @param financialZoneAccountStorage FinancialZoneAccountStorageコントラクト参照
     * @param finAccount FinancialZoneAccountsAll構造体
     */
    function setFinAccountAll(
        IFinancialZoneAccountStorage financialZoneAccountStorage,
        FinancialZoneAccountsAll memory finAccount
    ) external {
        financialZoneAccountStorage.setFinAccountAll(finAccount);
    }
}
