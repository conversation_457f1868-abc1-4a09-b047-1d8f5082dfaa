// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IValidatorStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev ValidatorLogicCallLibライブラリ
 *      Validatorのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */
library ValidatorLogicCallLib {
    /** @dev 検索結果の最大取得件数 */
    uint256 private constant MAX_LIMIT = 100;
    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant MAX_LIMIT_1000 = 1000;
    /** @dev 未登録の場合にて返す空の値 */
    bytes32 private constant EMPTY_VALUE = 0x00;
    /** @dev 未登録の場合にて返す空の件数 */
    uint256 private constant EMPTY_LENGTH = 0;
    /** @dev 共通領域のID */
    uint16 private constant FINANCIAL_ZONE = 3000;
    /** @dev ソート制御用の固定値(降順) */
    bytes32 private constant DESC_SORT = keccak256(bytes("desc"));
    /** @dev バリデーション用のステータス値(申し込み) */
    bytes32 private constant STATUS_APPLYING = "applying";
    /** @dev バリデーション用のステータス値(解約申し込み) */
    bytes32 private constant STATUS_TERMINATING = "terminating";

    /**
     * @dev 検証者追加時の入力パラメータ検証
     *
     * @param contractManager コントラクト管理
     * @param validatorStorage 検証者ストレージ
     * @param issuerId 申請者ID
     * @param validatorId 検証者ID
     */
    function checkAddValidatorIsValid(
        IContractManager contractManager,
        IValidatorStorage validatorStorage,
        bytes32 issuerId,
        bytes32 validatorId
    ) external view {
        require(validatorId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            !validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE1014_VALIDATOR_ID_EXIST
        );

        // zoneIdが登録されているか判別を行う
        (uint16 zoneId, , string memory errZone) = contractManager.provider().getZone();
        require(bytes(errZone).length == 0, errZone);

        // 共通領域の場合はIssuerIdのチェックを行う
        if (zoneId == FINANCIAL_ZONE) {
            // IssuerIdの存在チェック
            require(issuerId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
            // IssuerIdが未登録か確認を行う
            (bool success, string memory errIssuer) = contractManager.issuer().hasIssuer(issuerId);
            require(success, errIssuer);
            // 既にIssuerIDが紐付けられているか確認を行う
            require(
                !validatorStorage.getIssuerIdLinkedFlag(issuerId),
                Error.GE1004_ISSUER_ID_EXIST
            );
        }
    }

    /**
     * @dev ValidatorIDとAccountIDの存在チェックを統合
     *
     * @param validatorStorage ValidatorStorageコントラクト参照
     * @param validatorId validatorId
     * @param accountId accountId
     */
    function checkValidatorAndAccountExist(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 accountId
    ) external view {
        require(validatorId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE0108_VALIDATOR_ID_NOT_EXIST
        );
        require(accountId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );
    }

    /**
     * @dev validatorIdの入力検証
     *
     * @param validatorId チェック対象となる検証者ID
     * @return success true:有効,false:無効
     * @return err エラーメッセージ
     */
    function checkValidatorIdIsValid(bytes32 validatorId)
        external
        pure
        returns (bool success, string memory err)
    {
        // ValidatorId入力確認
        if (validatorId == EMPTY_VALUE) {
            return (false, Error.RV0009_VALIDATOR_INVALID_VAL);
        }
        return (true, "");
    }

    /**
     * @dev 検証者IDが登録済であるか確認する。
     *
     * @param exists 存在フラグ
     * @param validatorId チェック対象となる検証者ID
     * @return success true:登録済,false:未登録
     * @return err エラーメッセージ
     */
    function checkValidatorExists(bool exists, bytes32 validatorId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (validatorId == EMPTY_VALUE) {
            return (false, Error.RV0009_VALIDATOR_INVALID_VAL);
        }

        // Validator存在確認
        success = exists;
        err = success ? "" : Error.GE0108_VALIDATOR_ID_NOT_EXIST;

        return (success, err);
    }

    /**
     * @dev 指定されたValidatorIDにAccountが紐付いているか確認を行う。
     *
     * @param exists アカウントIDが検証者IDに紐付き済フラグ
     * @param accountId accountId
     * @return success true:紐づいている,false:紐づいていない
     * @return err エラーメッセージ
     */
    function checkAccountExists(bool exists, bytes32 accountId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (accountId == EMPTY_VALUE) {
            return (false, Error.RV0009_VALIDATOR_INVALID_VAL);
        }

        // Account存在確認
        if (!exists) {
            return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev アカウント同期時のステータス検証
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId validatorId
     * @param accountId accountId
     * @param accountStatus アカウントステータス
     */
    function checkSyncAccountStatusIsValid(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 accountId,
        bytes32 accountStatus
    ) external view {
        bool accountExistsInValidator = validatorStorage.getAccountIdExistenceByValidatorId(
            validatorId,
            accountId
        );
        if (accountStatus == STATUS_APPLYING) {
            // Accountが既に紐付けられているか確認を行う
            require(!accountExistsInValidator, Error.GE1010_ACCOUNT_ID_EXIST);
        } else if (accountStatus == STATUS_TERMINATING) {
            // Accountが既に紐付けられているか確認を行う
            require(accountExistsInValidator, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }
    }

    /**
     * @dev バリデータが直接管理するアカウントIDの存在確認
     *
     * @param validatorAccountId バリデータアカウントID
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function checkValidatorAccountIdExists(bytes32 validatorAccountId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (validatorAccountId == EMPTY_VALUE) {
            return (false, Error.GE0109_VALIDATOR_ACCOUNT_NOT_EXIST);
        }
        return (true, "");
    }

    ///////////////////////////////////
    // ValidatorStorage Wrapper Functions
    ///////////////////////////////////

    /**
     * @dev バリデータIDの存在確認をストレージから取得
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId バリデータID
     * @return exists 存在フラグ
     */
    function getValidatorIdExistence(IValidatorStorage validatorStorage, bytes32 validatorId)
        external
        view
        returns (bool)
    {
        return validatorStorage.getValidatorIdExistence(validatorId);
    }

    /**
     * @dev バリデータIDに紐づくアカウントIDの存在確認をストレージから取得
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId バリデータID
     * @param accountId アカウントID
     * @return exists 存在フラグ
     */
    function getAccountIdExistenceByValidatorId(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 accountId
    ) external view returns (bool) {
        return validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId);
    }

    /**
     * @dev バリデータデータをストレージから取得
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId バリデータID
     * @return validatorData バリデータデータ
     */
    function getValidatorData(IValidatorStorage validatorStorage, bytes32 validatorId)
        external
        view
        returns (ValidatorData memory)
    {
        return validatorStorage.getValidatorData(validatorId);
    }

    /**
     * @dev バリデータ数をストレージから取得
     *
     * @param validatorStorage ValidatorStorage参照
     * @return count バリデータ数
     */
    function getValidatorIdsCount(IValidatorStorage validatorStorage)
        external
        view
        returns (uint256)
    {
        return validatorStorage.getValidatorIdsCount();
    }

    /**
     * @dev インデックスによるバリデータIDをストレージから取得
     *
     * @param validatorStorage ValidatorStorage参照
     * @param index インデックス
     * @return validatorId バリデータID
     */
    function getValidatorIdByIndex(IValidatorStorage validatorStorage, uint256 index)
        external
        view
        returns (bytes32)
    {
        return validatorStorage.getValidatorIdByIndex(index);
    }

    /**
     * @dev バックアップ用の全バリデータデータを取得
     *
     * @param validatorStorage ValidatorStorage参照
     * @param index インデックス
     * @return validator 全バリデータデータ
     */
    function getValidatorAll(IValidatorStorage validatorStorage, uint256 index)
        external
        view
        returns (ValidatorAll memory)
    {
        return validatorStorage.getValidatorAll(index);
    }

    /**
     * @dev 検証者情報リストを取得する。
     *
     * @param validatorStorage ValidatorStorageコントラクト参照
     * @param limit limit
     * @param offset offset
     * @return validators validators
     * @return totalCount validatorの数
     * @return err エラーメッセージ
     */
    function getValidatorList(
        IValidatorStorage validatorStorage,
        uint256 limit,
        uint256 offset
    )
        external
        view
        returns (
            ValidatorListData[] memory validators,
            uint256 totalCount,
            string memory err
        )
    {
        uint256 validatorCount = validatorStorage.getValidatorIdsCount();
        // Inline validation
        if (limit == 0 || validatorCount == 0) return (new ValidatorListData[](0), 0, "");
        if (limit > MAX_LIMIT)
            return (new ValidatorListData[](0), 0, Error.UE0111_VALIDATOR_TOO_LARGE_LIMIT);
        if (offset >= validatorCount)
            return (new ValidatorListData[](0), 0, Error.UE0112_VALIDATOR_OFFSET_OUT_OF_INDEX);

        uint256 size = (validatorCount >= offset + limit) ? limit : validatorCount - offset;
        validators = new ValidatorListData[](size);
        for (uint256 i = 0; i < size; i++) {
            bytes32 validatorId = validatorStorage.getValidatorIdByIndex(offset + i);
            ValidatorData memory data = validatorStorage.getValidatorData(validatorId);
            validators[i] = ValidatorListData({
                validatorId: validatorId,
                name: data.name,
                issuerId: data.issuerId
            });
        }
        return (validators, validatorCount, "");
    }

    /**
     * @dev Zoneエラーがないことを確認
     *
     * @param zoneErr Zoneエラー文字列
     */
    function checkZoneHasNoError(string memory zoneErr) external pure {
        require(bytes(zoneErr).length == 0, zoneErr);
    }

    /**
     * @dev 共通領域のアカウントに連携済みのzone情報を取得
     *
     * @param contractManager ContractManager参照
     * @param accountId accountId
     * @param zoneId ゾーンID
     * @return zones zoneIdの配列
     * @return err エラーメッセージ
     */
    function getZonesForFinancialAccount(
        IContractManager contractManager,
        bytes32 accountId,
        uint16 zoneId
    ) external view returns (ZoneData[] memory zones, string memory err) {
        if (zoneId != FINANCIAL_ZONE) {
            return (new ZoneData[](0), "");
        }
        zones = contractManager.account().getZoneByAccountId(accountId);
        return (zones, "");
    }

    /**
     * @dev Validator権限追加の検証処理
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId validatorId
     * @param validatorEoa validatorEoaアドレス
     */
    function checkAddValidatorRoleIsValid(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        address validatorEoa
    ) external view {
        require(validatorId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE0108_VALIDATOR_ID_NOT_EXIST
        );
        require(validatorEoa != address(0), Error.RV0009_VALIDATOR_INVALID_VAL);
    }

    /**
     * @dev Validator名更新の検証処理
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId validatorId
     */
    function checkModValidatorIsValid(IValidatorStorage validatorStorage, bytes32 validatorId)
        external
        view
    {
        require(validatorId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE0108_VALIDATOR_ID_NOT_EXIST
        );
    }

    /**
     * @dev Account名更新の検証処理
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId validatorId
     * @param accountId accountId
     */
    function checkModAccountIsValid(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 accountId
    ) external view {
        require(validatorId != EMPTY_VALUE, Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE0108_VALIDATOR_ID_NOT_EXIST
        );
        require(accountId != EMPTY_VALUE, Error.RV0007_ACCOUNT_INVALID_VAL);
        require(
            validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );
    }

    /**
     * @dev Account追加の検証処理
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId validatorId
     * @param accountId accountId
     */
    function checkAddAccountIsValid(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 accountId
    ) external view {
        require(validatorId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE0108_VALIDATOR_ID_NOT_EXIST
        );
        require(
            !validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId),
            Error.GE1010_ACCOUNT_ID_EXIST
        );
    }

    /**
     * @dev バリデータが直接管理するアカウントID追加の検証処理
     *
     * @param contractManager ContractManager参照
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId バリデータID
     * @param accountId アカウントID
     */
    function checkAddValidatorAccountIdIsValid(
        IContractManager contractManager,
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 accountId
    ) external view {
        require(validatorId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE0108_VALIDATOR_ID_NOT_EXIST
        );
        require(accountId != bytes32(0), Error.RV0007_ACCOUNT_INVALID_VAL);
        (bool accountExists, ) = contractManager.account().hasAccount(accountId);
        require(accountExists, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        require(
            validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );
    }

    /**
     * @dev Validatorに紐づくAccountの情報を取得する。（限度額情報付き）
     *
     * @param validatorStorage ValidatorStorage参照
     * @param contractManager ContractManager参照
     * @param validatorId validatorId
     * @param accountId accountId
     * @return accountData アカウントデータ（限度額情報付き）
     * @return err エラーメッセージ
     */
    function getAccountWithLimits(
        IValidatorStorage validatorStorage,
        IContractManager contractManager,
        bytes32 validatorId,
        bytes32 accountId
    ) external view returns (AccountDataWithLimitData memory accountData, string memory err) {
        // Validation
        if (validatorId == bytes32(0)) {
            return (accountData, Error.RV0009_VALIDATOR_INVALID_VAL);
        }
        if (accountId == bytes32(0)) {
            return (accountData, Error.RV0009_VALIDATOR_INVALID_VAL);
        }
        if (!validatorStorage.getValidatorIdExistence(validatorId)) {
            return (accountData, Error.GE0108_VALIDATOR_ID_NOT_EXIST);
        }
        if (!validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId)) {
            return (accountData, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }

        // Get base account data
        AccountDataWithoutZoneId memory baseData;
        (baseData, err) = contractManager.account().getAccount(accountId);
        if (bytes(err).length > 0) return (accountData, err);

        // Copy base data
        accountData.accountName = baseData.accountName;
        accountData.accountStatus = baseData.accountStatus;
        accountData.balance = baseData.balance;
        accountData.reasonCode = baseData.reasonCode;
        accountData.appliedAt = baseData.appliedAt;
        accountData.registeredAt = baseData.registeredAt;
        accountData.terminatingAt = baseData.terminatingAt;
        accountData.terminatedAt = baseData.terminatedAt;

        // Add limits for Financial Zone only
        (uint16 zoneId, , ) = contractManager.provider().getZone();
        if (zoneId == FINANCIAL_ZONE) {
            FinancialZoneAccountData memory limitData;
            (limitData, err) = contractManager.account().getAccountLimit(validatorId, accountId);
            if (bytes(err).length == 0) {
                accountData.mintLimit = limitData.mintLimit;
                accountData.burnLimit = limitData.burnLimit;
                accountData.transferLimit = limitData.transferLimit;
                accountData.chargeLimit = limitData.chargeLimit;
                accountData.dischargeLimit = limitData.dischargeLimit;
                accountData.cumulativeLimit = limitData.cumulativeLimit;
                accountData.cumulativeAmount = limitData.cumulativeAmount;
                accountData.cumulativeDate = limitData.cumulativeDate;
                accountData.cumulativeTransactionLimits = limitData.cumulativeTransactionLimits;
            }
        }
        return (accountData, "");
    }

    /**
     * @dev 該当ValidatorIDに紐づくAccountの情報を取得する。
     *
     * @param validatorStorage ValidatorStorage参照
     * @param contractManager ContractManager参照
     * @param validatorId validatorId
     * @param offset オフセット
     * @param limit リミット
     * @param sortOrder ソート順(desc: 降順, asc: 昇順)
     * @return accounts ValidatorAccountsData[]
     * @return totalCount
     * @return err エラーメッセージ
     */
    function getValidatorAccountList(
        IValidatorStorage validatorStorage,
        IContractManager contractManager,
        bytes32 validatorId,
        uint256 offset,
        uint256 limit,
        string memory sortOrder
    )
        external
        view
        returns (
            ValidatorAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        // Validation
        if (!validatorStorage.getValidatorIdExistence(validatorId)) {
            return (new ValidatorAccountsData[](0), 0, Error.GE0108_VALIDATOR_ID_NOT_EXIST);
        }

        // Sort account IDs inline
        bytes32[] memory accountIds = validatorStorage.getValidatorData(validatorId).accountIds;
        if (keccak256(bytes(sortOrder)) == DESC_SORT) {
            uint256 n = accountIds.length;
            for (uint256 i = 0; i < n / 2; i++) {
                bytes32 temp = accountIds[i];
                accountIds[i] = accountIds[n - i - 1];
                accountIds[n - i - 1] = temp;
            }
        }

        if (limit == 0 || accountIds.length == 0) {
            return (accounts, EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT) {
            return (accounts, EMPTY_LENGTH, Error.UE0107_ACCOUNT_TOO_LARGE_LIMIT);
        }
        if (offset >= accountIds.length) {
            return (accounts, EMPTY_LENGTH, Error.UE0108_ACCOUNT_OFFSET_OUT_OF_INDEX);
        }

        uint256 size = (accountIds.length >= offset + limit) ? limit : accountIds.length - offset;
        accounts = new ValidatorAccountsData[](size);

        for (uint256 i = 0; i < size; i++) {
            bytes32 accountId = accountIds[offset + i];
            AccountDataWithoutZoneId memory accountData;
            accounts[i].accountId = accountId;
            (accountData, ) = contractManager.account().getAccount(accountId);
            accounts[i].accountName = accountData.accountName;
            accounts[i].balance = accountData.balance;
            accounts[i].accountStatus = accountData.accountStatus;
            accounts[i].reasonCode = accountData.reasonCode;
            accounts[i].appliedAt = accountData.appliedAt;
            accounts[i].registeredAt = accountData.registeredAt;
            accounts[i].terminatingAt = accountData.terminatingAt;
            accounts[i].terminatedAt = accountData.terminatedAt;
        }
        return (accounts, accountIds.length, "");
    }

    /**
     * @dev (CoreBatch専用 BCClient経由)該当ValidatorIDに紐づくAccountの情報を取得する。
     * @dev レスポンスのValidatorAccountsDataのソート順について
     * @dev validatorMapping[validatorId].accountIdsの格納順の逆順となる。
     *
     * @param validatorStorage ValidatorStorage参照
     * @param contractManager ContractManager参照
     * @param validatorId validatorId
     * @param offset オフセット
     * @param limit リミット
     * @return accounts ValidatorAccountsDataALL[]
     * @return totalCount
     * @return err エラーメッセージ
     */
    function getValidatorAccountAllList(
        IValidatorStorage validatorStorage,
        IContractManager contractManager,
        bytes32 validatorId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            ValidatorAccountsDataALL[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        // Validation
        if (!validatorStorage.getValidatorIdExistence(validatorId)) {
            return (new ValidatorAccountsDataALL[](0), 0, Error.GE0108_VALIDATOR_ID_NOT_EXIST);
        }

        // Sort account IDs inline (desc)
        bytes32[] memory accountIds = validatorStorage.getValidatorData(validatorId).accountIds;
        uint256 n = accountIds.length;
        for (uint256 i = 0; i < n / 2; i++) {
            bytes32 temp = accountIds[i];
            accountIds[i] = accountIds[n - i - 1];
            accountIds[n - i - 1] = temp;
        }

        if (limit == 0 || accountIds.length == 0) {
            return (new ValidatorAccountsDataALL[](0), EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT_1000) {
            return (
                new ValidatorAccountsDataALL[](0),
                EMPTY_LENGTH,
                Error.UE0107_ACCOUNT_TOO_LARGE_LIMIT
            );
        }
        if (offset >= accountIds.length) {
            return (
                new ValidatorAccountsDataALL[](0),
                EMPTY_LENGTH,
                Error.UE0108_ACCOUNT_OFFSET_OUT_OF_INDEX
            );
        }

        uint256 size = (accountIds.length >= offset + limit) ? limit : accountIds.length - offset;
        accounts = new ValidatorAccountsDataALL[](size);

        for (uint256 i = 0; i < size; i++) {
            bytes32 accountId = accountIds[offset + i];
            AccountDataAll memory accountData;
            accounts[i].accountId = accountId;
            (accountData, ) = contractManager.account().getAccountAll(accountId);
            accounts[i].accountDataAll = accountData;
        }
        return (accounts, accountIds.length, "");
    }
}
