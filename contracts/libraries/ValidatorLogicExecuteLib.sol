// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IValidatorStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev ValidatorLogicExecuteLibライブラリ
 *      Validatorの実行関数を実装するヘルパーライブラリ
 */
library ValidatorLogicExecuteLib {
    /** @dev 共通領域のID */
    uint16 private constant FINANCIAL_ZONE = 3000;
    /** @dev バリデーション用のステータス値(申し込み) */
    bytes32 private constant STATUS_APPLYING = "applying";
    /** @dev バリデーション用のステータス値(解約申し込み) */
    bytes32 private constant STATUS_TERMINATING = "terminating";

    /**
     * @dev BizZone向けAccount登録処理（同期処理）
     *
     * @param validatorStorage ValidatorStorage参照
     * @param contractManager ContractManager参照
     * @param validatorId validatorId
     * @param accountId accountId
     * @param accountName account名
     * @param accountStatus 口座のステータス
     * @param reasonCode 理由コード
     * @param approvalAmount 承認額
     * @param traceId トレースID
     */
    function executeSyncAccount(
        IValidatorStorage validatorStorage,
        IContractManager contractManager,
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        bytes32 accountStatus,
        bytes32 reasonCode,
        uint256 approvalAmount,
        bytes32 traceId
    ) external {
        if (accountStatus == STATUS_APPLYING) {
            contractManager.account().addAccount(accountId, accountName, validatorId);
            validatorStorage.addAccountIdToValidator(validatorId, accountId);
            validatorStorage.setAccountIdExistenceByValidatorId(validatorId, accountId, true);

            if (approvalAmount > 0) {
                bytes32 validatorAccountId = validatorStorage
                    .getValidatorData(validatorId)
                    .validatorAccountId;
                require(validatorAccountId != bytes32(0), Error.GE0109_VALIDATOR_ACCOUNT_NOT_EXIST);
                contractManager.token().approve(
                    validatorId,
                    accountId,
                    validatorAccountId,
                    approvalAmount,
                    traceId
                );
            }
        } else if (accountStatus == STATUS_TERMINATING) {
            contractManager.validator().setTerminated(validatorId, accountId, reasonCode, traceId);
        }
    }

    /**
     * @dev Validator権限の追加処理（アクセス制御含む）
     *
     * @param validatorStorage ValidatorStorage参照
     * @param contractManager ContractManager参照
     * @param validatorId validatorId
     * @param validatorEoa validatorEoaアドレス
     */
    function executeAddValidatorRole(
        IValidatorStorage validatorStorage,
        IContractManager contractManager,
        bytes32 validatorId,
        address validatorEoa
    ) external {
        bytes32 rolePrefix = keccak256("VALIDATOR_ROLE");
        bytes32 role = contractManager.accessCtrl().calcRole(rolePrefix, validatorId);
        validatorStorage.updateValidatorRole(validatorId, role);
        // Update enabled flag to true when role is added
        ValidatorData memory data = validatorStorage.getValidatorData(validatorId);
        data.enabled = true;
        validatorStorage.setValidatorData(validatorId, data);
        contractManager.accessCtrl().addRoleByValidator(validatorId, role, validatorEoa);
    }

    /**
     * @dev Accountを限度額付きで登録する（共通領域用）
     *
     * @param validatorStorage ValidatorStorage参照
     * @param contractManager ContractManager参照
     * @param validatorId validatorId
     * @param accountId accountId
     * @param accountName account名
     * @param limitValues アカウントの限度額値
     * @param traceId トレースID
     */
    function executeAddAccountWithLimits(
        IValidatorStorage validatorStorage,
        IContractManager contractManager,
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        AccountLimitValues memory limitValues,
        bytes32 traceId
    ) external {
        contractManager.account().addAccount(accountId, accountName, validatorId);
        contractManager.financialZoneAccount().addAccountLimit(accountId, limitValues, traceId);

        // Issuerとの紐付け処理
        ValidatorData memory validatorData = validatorStorage.getValidatorData(validatorId);
        bytes32 issuerId = validatorData.issuerId;
        // IssuerとAccountを紐付ける
        contractManager.issuer().addAccountId(issuerId, accountId, traceId);

        validatorStorage.addAccountIdToValidator(validatorId, accountId);
        validatorStorage.setAccountIdExistenceByValidatorId(validatorId, accountId, true);
    }

    /**
     * @dev Validatorの追加処理（検証とセットアップ）
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId validatorId
     * @param issuerId issuerId
     * @param name validator名
     */
    function executeAddValidator(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 issuerId,
        bytes32 name
    ) external {
        ValidatorData memory validatorData = ValidatorData({
            name: name,
            issuerId: issuerId,
            role: bytes32(0),
            enabled: false,
            validatorAccountId: bytes32(0),
            accountIds: new bytes32[](0)
        });

        validatorStorage.setValidatorData(validatorId, validatorData);
        validatorStorage.setValidatorIdExistence(validatorId, true);
        // issuerIdが設定されている場合はフラグを設定
        if (issuerId != bytes32(0)) {
            validatorStorage.setIssuerIdLinkedFlag(issuerId, true);
        }
        validatorStorage.addValidatorId(validatorId);
    }

    ///////////////////////////////////
    // ValidatorStorage Wrapper Functions
    ///////////////////////////////////

    /**
     * @dev バリデータ名を更新
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId バリデータID
     * @param name 新しい名前
     */
    function updateValidatorName(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 name
    ) external {
        validatorStorage.updateValidatorName(validatorId, name);
    }

    /**
     * @dev バリデータアカウントIDを更新
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validatorId バリデータID
     * @param validatorAccountId バリデータアカウントID
     */
    function updateValidatorAccountId(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 validatorAccountId
    ) external {
        validatorStorage.updateValidatorAccountId(validatorId, validatorAccountId);
    }

    /**
     * @dev バックアップ用に全バリデータデータを設定
     *
     * @param validatorStorage ValidatorStorage参照
     * @param validator 全バリデータデータ
     * @param deadline 署名期限
     * @param signature 署名
     */
    function setValidatorAll(
        IValidatorStorage validatorStorage,
        ValidatorAll memory validator,
        uint256 deadline,
        bytes memory signature
    ) external {
        validatorStorage.setValidatorAll(validator, deadline, signature);
    }
}
