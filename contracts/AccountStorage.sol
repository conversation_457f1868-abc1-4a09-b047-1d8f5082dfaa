// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IAccountStorage.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/Error.sol";
import "./interfaces/Struct.sol";

import "./remigration/RemigrationLib.sol";

/**
 * @dev AccountStorageコントラクト
 *      Accountデータのストレージ管理を行う
 *      CRUDのみを実装し、ビジネスロジックは含まない
 */
contract AccountStorage is Initializable, IAccountStorage {
    using RemigrationLib for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////
    uint256 private constant MAX_LIMIT = 100;
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant EMPTY_LENGTH = 0;
    /** @dev バリデーション用のステータス値(解約済) */
    bytes32 private constant STATUS_TERMINATED = "terminated";
    /** @dev バリデーション用のステータス値(アクティブ) */
    bytes32 private constant STATUS_ACTIVE = "active";
    /** @dev 未登録の場合にて返す空の値 */
    bytes32 private constant EMPTY_BYTES32 = 0x00;

    /** @dev AccountLogicコントラクトアドレス */
    address private _accountLogicAddr;

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev アカウントID */
    bytes32[] private _accountIds;
    /** @dev アカウントIDの存在確認フラグ(accountId => boolean) */
    mapping(bytes32 => bool) private _accountIdExistence;
    /** @dev アカウントデータ(accountId => AccountData) */
    mapping(bytes32 => AccountData) private _accountData;
    /** @dev  登録先のアカウント許可額データマッピング */
    mapping(bytes32 => AllowanceList) private _accountApprovalMapping;

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    /**
     * @dev AccountLogicコントラクトからのみ呼び出し可能を保証するmodifier
     */
    modifier accountLogicOnly() {
        require(msg.sender == _accountLogicAddr, Error.GA0030_INVALID_CALLER_ADDRESS);
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     * @param contractManager ContractManagerアドレス
     * @param accountLogicAddr AccountLogicコントラクトアドレス
     */
    function initialize(IContractManager contractManager, address accountLogicAddr)
        public
        initializer
    {
        require(address(contractManager) != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);
        require(accountLogicAddr != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);
        _contractManager = contractManager;
        _accountLogicAddr = accountLogicAddr;
    }

    /**
     * @dev コントラクトバージョン取得
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @dev アカウントの登録
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountName アカウント名
     */
    function addAccountData(bytes32 accountId, string memory accountName)
        external
        accountLogicOnly
    {
        _accountData[accountId].accountName = accountName;
        _accountData[accountId].accountStatus = STATUS_ACTIVE;
        _accountData[accountId].registeredAt = block.timestamp; // 登録日時として現在のブロックタイムスタンプを設定
    }

    /**
     * @dev アカウントID登録
     * @param accountId マッピングのキーとなる追加対象アカウントID
     */
    function addAccountId(bytes32 accountId) external accountLogicOnly {
        _accountIds.push(accountId);
    }

    /**
     * @dev accountの存在フラグを取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return exists 存在フラグ
     */
    function getAccountIdExistence(bytes32 accountId)
        external
        view
        accountLogicOnly
        returns (bool exists)
    {
        return _accountIdExistence[accountId];
    }

    /**
     * @dev accountの存在フラグを設定する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param exists 存在フラグ
     */
    function addAccountIdExistence(bytes32 accountId, bool exists) external accountLogicOnly {
        _accountIdExistence[accountId] = exists;
    }

    /**
     * @dev アカウントに紐づくvalidatorIdの登録
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param validatorId バリデータID
     */
    function addValidatorId(bytes32 accountId, bytes32 validatorId) external accountLogicOnly {
        _accountData[accountId].validatorId = validatorId;
    }

    /**
     * @dev accountIdを指定してvalidatorIdを取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return validatorId バリデータID
     */
    function getValidatorId(bytes32 accountId)
        external
        view
        accountLogicOnly
        returns (bytes32 validatorId)
    {
        return _accountData[accountId].validatorId;
    }

    /**
     * @dev アカウント名の変更
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountName アカウント名
     */
    function updateAccountName(bytes32 accountId, string memory accountName)
        external
        accountLogicOnly
    {
        _accountData[accountId].accountName = accountName;
    }

    /**
     * @dev アカウントステータス情報を取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountStatus アカウントステータス
     */
    function getAccountStatus(bytes32 accountId)
        external
        view
        accountLogicOnly
        returns (bytes32 accountStatus)
    {
        return _accountData[accountId].accountStatus;
    }

    /**
     * @dev Accountの状態を更新する(凍結 or アクティブ)
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     * @param reasonCode 理由コード
     */
    function setAccountStatusAndReasonCode(
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode
    ) external accountLogicOnly {
        _accountData[accountId].accountStatus = accountStatus;
        _accountData[accountId].reasonCode = reasonCode;
    }

    /**
     * @dev accountの状態を更新する(アクティブ)
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     */
    function setAccountStatus(bytes32 accountId, bytes32 accountStatus) external accountLogicOnly {
        _accountData[accountId].accountStatus = accountStatus;
    }

    /**
     * @dev アカウントのステータスを解約済みに更新する　TODO:他関数と統合する
     * @param accountId マッピングのキーとなるアカウントID
     * @param reasonCode 理由コード
     */
    function setTerminated(bytes32 accountId, bytes32 reasonCode) external accountLogicOnly {
        _accountData[accountId].accountStatus = STATUS_TERMINATED;
        _accountData[accountId].reasonCode = reasonCode;
        _accountData[accountId].terminatedAt = block.timestamp;
    }

    /**
     * @dev 連携済みzone情報の追加
     * @param accountId マッピングのキーとなるアカウントID
     * @param zoneId zoneId
     */
    function addZone(bytes32 accountId, uint16 zoneId) external accountLogicOnly {
        _accountData[accountId].zoneIds.push(zoneId);
    }

    /**
     * @dev アカウント名取得
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountName アカウント名
     */
    function getAccountName(bytes32 accountId)
        public
        view
        accountLogicOnly
        returns (string memory accountName)
    {
        return _accountData[accountId].accountName;
    }

    /**
     * @dev カウントの残高取得
     * @param accountId マッピングのキーとなるアカウントID
     * @return balance アカウントの残高
     */
    function getAccountBalance(bytes32 accountId)
        external
        view
        accountLogicOnly
        returns (uint256 balance)
    {
        return _accountData[accountId].balance;
    }

    /**
     * @dev accountの残高更新
     * @param accountId マッピングのキーとなるアカウントID
     * @param balance 指定の残高
     */
    function setAccountBalance(bytes32 accountId, uint256 balance) external accountLogicOnly {
        _accountData[accountId].balance = balance;
    }

    /**
     * @dev 送金許可設定。
     * @param ownerId ownerId
     * @param spenderId 送金許可対象者のID
     * @param approvedAt 支払い許可日時
     * @param amount 許容額
     */
    function setApprove(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 approvedAt,
        uint256 amount
    ) external accountLogicOnly {
        _accountApprovalMapping[ownerId].spender.push(spenderId);
        _accountApprovalMapping[ownerId].accountApprovalData[spenderId].approvedAmount = amount;
        _accountApprovalMapping[ownerId]
            .accountApprovalData[spenderId]
            .spenderAccountName = getAccountName(spenderId);
        _accountApprovalMapping[ownerId].accountApprovalData[spenderId].approvedAt = approvedAt;
    }

    /**
     * @dev 送金許可額の減額を行う。
     * @param ownerId ownerId
     * @param spenderId 送金許可対象者のID
     * @param amount 送金額
     */
    function setAllowance(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external accountLogicOnly {
        if (
            _accountApprovalMapping[ownerId].accountApprovalData[spenderId].approvedAmount >=
            Constant.MAX_ALLOWANCE_VALUE
        ) return;
        require(
            _accountApprovalMapping[ownerId].accountApprovalData[spenderId].approvedAmount >=
                amount,
            Error.UE4401_ALLOWANCE_NOT_ENOUGH
        );
        _accountApprovalMapping[ownerId].accountApprovalData[spenderId].approvedAmount -= amount;
    }

    /**
     * @dev バックアップ用に全発行者データを設定する（Admin権限必要）
     * @param account 全発行者データ
     */
    function setAccountAll(AccountsAll memory account) external accountLogicOnly {
        RemigrationLib.setAccountAll(
            _accountIds,
            _accountData[account.accountId],
            _accountIdExistence,
            _accountApprovalMapping,
            address(_contractManager),
            account
        );
    }

    /**
     * @dev Accountの情報を返す。
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountDataWithoutZoneId アカウントデータ(zoneIdなし)
     */
    function getAccountDataWithoutZoneId(bytes32 accountId)
        external
        view
        accountLogicOnly
        returns (AccountDataWithoutZoneId memory accountDataWithoutZoneId)
    {
        accountDataWithoutZoneId = AccountDataWithoutZoneId(
            _accountData[accountId].accountName,
            _accountData[accountId].accountStatus,
            _accountData[accountId].balance,
            _accountData[accountId].reasonCode,
            _accountData[accountId].appliedAt,
            _accountData[accountId].registeredAt,
            _accountData[accountId].terminatingAt,
            _accountData[accountId].terminatedAt
        );
        return accountDataWithoutZoneId;
    }

    /**
     * @dev account dataを返す。
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountData アカウントデータ
     */
    function getAccountData(bytes32 accountId)
        external
        view
        accountLogicOnly
        returns (AccountData memory accountData)
    {
        accountData = _accountData[accountId];
        return accountData;
    }

    /**
     * @dev IndexよりAccountIDを取得する。
     *
     * @param index index
     * @return accountId accountId
     * @return err エラーメッセージ
     */
    function getAccountId(uint256 index)
        external
        view
        accountLogicOnly
        returns (bytes32 accountId, string memory err)
    {
        if (_accountIds.length <= index) {
            return (EMPTY_BYTES32, Error.UE0106_ACCOUNT_OUT_OF_INDEX);
        }
        return (_accountIds[index], "");
    }

    /**
     * @dev アカウントの許可額を取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param index 許可対象のアカウントID
     * @return allowance 許可額
     * @return approvedAt 許可日付
     */
    function getAllowance(bytes32 accountId, bytes32 index)
        external
        view
        accountLogicOnly
        returns (uint256 allowance, uint256 approvedAt)
    {
        return (
            _accountApprovalMapping[accountId].accountApprovalData[index].approvedAmount,
            _accountApprovalMapping[accountId].accountApprovalData[index].approvedAt
        );
    }

    /**
     * @dev 送金許可一覧照会 TODO:Core APIとのマッピング時に作成
     * @param ownerId 送金許可元ID
     * @param offset オフセット
     * @param limit リミット
     * @return approvalData 送金許可設定一覧
     * @return totalCount 総数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        bytes32 ownerId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        accountLogicOnly
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        )
    {
        (bool success, string memory error) = _contractManager.account().hasAccount(ownerId);
        if (!success) {
            return (new AccountApprovalAll[](0), 0, error);
        }

        bytes32[] memory spenders = _accountApprovalMapping[ownerId].spender;
        AccountApprovalAll[] memory accountApprovalList = new AccountApprovalAll[](spenders.length);

        if (limit == 0 || spenders.length == 0) {
            return (accountApprovalList, EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT) {
            return (accountApprovalList, EMPTY_LENGTH, Error.UE0107_ACCOUNT_TOO_LARGE_LIMIT);
        }
        if (offset >= spenders.length) {
            return (accountApprovalList, EMPTY_LENGTH, Error.UE0108_ACCOUNT_OFFSET_OUT_OF_INDEX);
        }

        // 配列のサイズを作成する。
        // 要素数がoffsetとlimitの指定範囲以上の配列である場合はlimit分の配列を作成する
        // 要素数がoffsetとlimitの指定範囲未満の配列である場合は範囲分の項目数のみの配列を作成する
        uint256 size = (spenders.length >= offset + limit) ? limit : spenders.length - offset;

        accountApprovalList = new AccountApprovalAll[](size);

        for (uint256 i = 0; i < size; i++) {
            accountApprovalList[i].spanderId = spenders[offset + i];
            accountApprovalList[i].spenderAccountName = _accountApprovalMapping[ownerId]
                .accountApprovalData[spenders[offset + i]]
                .spenderAccountName;
            accountApprovalList[i].allowanceAmount = _accountApprovalMapping[ownerId]
                .accountApprovalData[spenders[offset + i]]
                .approvedAmount;
            accountApprovalList[i].approvedAt = _accountApprovalMapping[ownerId]
                .accountApprovalData[spenders[offset + i]]
                .approvedAt;
        }

        return (accountApprovalList, spenders.length, "");
    }

    /**
     * @dev Accountの数を返却する。
     * @return count accountの数
     */
    function getAccountCount() external view accountLogicOnly returns (uint256 count) {
        return _accountIds.length;
    }

    /**
     * @dev アカウントに連携済みのzoneIdの取得
     * @param accountId マッピングのキーとなるアカウントID
     * @return zoneIdList アカウントに連携済みのzoneIdのリスト
     */
    function getAccountZoneIdList(bytes32 accountId)
        external
        view
        accountLogicOnly
        returns (uint16[] memory zoneIdList)
    {
        return _accountData[accountId].zoneIds;
    }

    /**
     * @dev indexよりaccount詳細情報を取得する
     *
     * @param index マッピングのキーとなるアカウントID
     * @return account アカウント全ての情報
     */
    function getAccountAll(uint256 index) external view returns (AccountsAll memory account) {
        bytes32 _accountId = _accountIds[index];

        return
            RemigrationLib.getAccountAll(
                _accountData[_accountId],
                _accountIdExistence,
                _accountApprovalMapping,
                address(_contractManager),
                _accountId
            );
    }

    /**
     * @dev AccountLogicアドレスを更新する（Admin権限必要）
     * @param accountLogicAddr 新しいAccountLogicアドレス
     */
    function setAccountLogicAddress(address accountLogicAddr) external {
        require(accountLogicAddr != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);
        _accountLogicAddr = accountLogicAddr;
    }

    /**
     * @dev AccountLogicアドレスを取得する
     * @return accountLogicAddr AccountLogicアドレス
     */
    function getAccountLogicAddress() external view returns (address) {
        return _accountLogicAddr;
    }

    /**
     * @dev ContractManagerアドレスを更新する（将来の拡張用）
     * @param contractManagerAddr 新しいContractManagerアドレス
     */
    function setContractManagerAddress(address contractManagerAddr) external {
        require(contractManagerAddr != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);
        _contractManager = IContractManager(contractManagerAddr);
    }

    /**
     * @dev ContractManagerアドレスを取得する
     * @return contractManagerAddr ContractManagerアドレス
     */
    function getContractManagerAddress() external view returns (address) {
        return address(_contractManager);
    }
}
