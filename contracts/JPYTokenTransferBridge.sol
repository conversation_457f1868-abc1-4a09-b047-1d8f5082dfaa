// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import {Height} from "./yuiContracts/proto/Client.sol";
import {Packet} from "./yuiContracts/proto/Channel.sol";
import {IBCAppBase} from "./yuiContracts/apps/commons/IBCAppBase.sol";
import {IIBCModule} from "./yuiContracts/core/26-router/IIBCModule.sol";
import {IIBCHandler} from "./yuiContracts/core/25-handler/IIBCHandler.sol";

import "./interfaces/IIBCToken.sol";
import "./interfaces/IAccessCtrl.sol";
import "./interfaces/Error.sol";
import "./interfaces/Struct.sol";

contract JPYTokenTransferBridge is Initializable, IBCAppBase {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////
    IIBCHandler private _ibcHandler;
    IIBCToken private _ibcToken;
    IAccessCtrl private _accessCtrl;

    uint16 internal finZoneId;

    string public tokenTransferSourcePort;
    string public tokenTransferSourceChannel;
    string public tokenTransferSourceVersion;
    mapping(uint16 => string) private _zoneIdToChannel;

    // escrow accounts
    // mapping (srcZoneId => mapping (dstZoneId => escrow account))
    mapping(uint16 => mapping(uint16 => bytes32)) private _escrowAccounts;

    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_TOKEN_TRANSFER = "tokenTransfer";

    ///////////////////////////////////
    // modifier
    ///////////////////////////////////

    modifier adminOnly(uint256 deadline, bytes memory signature) {
        bytes32 hash = keccak256(abi.encode(_STRING_TOKEN_TRANSFER, deadline));

        (bool has, string memory err) = _accessCtrl.checkAdminRole(hash, deadline, signature);
        require(bytes(err).length == 0, err);
        require(has, "not admin role");
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param ibcHandler_ ibcHandler
     * @param ibcTokenAddr ibcToken
     * @param accessCtrlAddr accessCtrl
     */
    function initialize(
        IIBCHandler ibcHandler_,
        address ibcTokenAddr,
        address accessCtrlAddr
    ) public initializer {
        require(address(ibcHandler_) != address(0), Error.RV0010_IBC_INVALID_VAL);
        require(ibcTokenAddr != address(0), Error.RV0010_IBC_INVALID_VAL);
        require(accessCtrlAddr != address(0), Error.RV0010_IBC_INVALID_VAL);
        _ibcHandler = ibcHandler_;
        _ibcToken = IIBCToken(ibcTokenAddr);
        _accessCtrl = IAccessCtrl(accessCtrlAddr);

        finZoneId = 3000;
        tokenTransferSourcePort = "token-transfer";
        tokenTransferSourceChannel = "channel-2";
        tokenTransferSourceVersion = "token-transfer-1";
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return version コントラクトバージョン
     */
    function version() external pure virtual returns (string memory) {
        return "v1";
    }

    function ibcAddress() public view virtual override returns (address) {
        return address(_ibcHandler);
    }

    function setAddress(
        IIBCToken ibcTokenAddr,
        IAccessCtrl accessCtrlAddr,
        uint256 deadline,
        bytes memory signature
    ) public adminOnly(deadline, signature) {
        _ibcToken = ibcTokenAddr;
        _accessCtrl = accessCtrlAddr;
    }

    function setChannel(
        uint16 zoneId,
        string memory channel,
        uint256 deadline,
        bytes memory signature
    ) public adminOnly(deadline, signature) {
        _zoneIdToChannel[zoneId] = channel;
    }

    function _isNativeRegion(uint16 srcZoneId) private view returns (bool) {
        return srcZoneId == finZoneId;
    }

    function getConfig() external view returns (Config memory config) {
        config = Config({
            port: tokenTransferSourcePort,
            channel: tokenTransferSourceChannel,
            version: tokenTransferSourceVersion
        });

        return config;
    }

    // TODO: チャネル情報とZone情報の管理・変更がある場合変更できる機能が必要
    // Admin権限で実装すること
    // setConfig()
    // setFinZoneId()

    function registerEscrowAccount(
        uint16 srcZoneId,
        uint16 dstZoneId,
        bytes32 eAccount,
        uint256 deadline,
        bytes memory signature
    ) external {
        bytes32 hash = keccak256(abi.encode(srcZoneId, dstZoneId, eAccount, deadline));
        (bool has, string memory errTmp) = _ibcToken.checkAdminRole(hash, deadline, signature);
        require(bytes(errTmp).length == 0, errTmp);
        require(has, Error.GA0023_IBC_NOT_ADMIN_ROLE);
        require(
            _escrowAccounts[srcZoneId][dstZoneId] == 0x00,
            Error.GE1016_IBC_APP_JPYTT_ESCROW_ALWAYS_REG
        );
        _escrowAccounts[srcZoneId][dstZoneId] = eAccount;
    }

    function unregisterEscrowAccount(
        uint16 srcZoneId,
        uint16 dstZoneId,
        uint256 deadline,
        bytes memory signature
    ) external {
        bytes32 hash = keccak256(abi.encode(srcZoneId, dstZoneId, deadline));
        (bool has, string memory errTmp) = _ibcToken.checkAdminRole(hash, deadline, signature);
        require(bytes(errTmp).length == 0, errTmp);
        require(has, Error.GA0023_IBC_NOT_ADMIN_ROLE);
        _escrowAccounts[srcZoneId][dstZoneId] = 0x00;
    }

    function escrowAccount(uint16 srcZoneId, uint16 dstZoneId) external view returns (bytes32) {
        return _escrowAccounts[srcZoneId][dstZoneId];
    }

    // From Contract: ICS-20送金を指示
    function transfer(
        bytes32 accountId,
        uint16 fromZoneId,
        uint16 toZoneId,
        uint256 amount,
        uint64 timeoutHeight,
        bytes32 traceId
    ) external returns (uint256) {
        require(
            _isNativeRegion(fromZoneId) != _isNativeRegion(toZoneId),
            Error.RV0010_IBC_INVALID_VAL
        );

        if (_isNativeRegion(fromZoneId)) {
            bytes32 ea = _escrowAccounts[fromZoneId][toZoneId];
            require(ea != 0x00, Error.GE0110_IBC_APP_JPYTT_ESCROW_NOT_REG);
            _ibcToken.transferToEscrow(toZoneId, accountId, ea, amount, traceId);
        } else {
            _ibcToken.redeemVoucher(accountId, amount, traceId);
        }

        SyncTokenTransferParams memory packetData = SyncTokenTransferParams({
            accountId: accountId,
            fromZoneId: fromZoneId,
            toZoneId: toZoneId,
            amount: amount,
            transferType: Constant.CHARGE,
            traceId: traceId
        });

        bytes memory packet = abi.encode(packetData);

        return
            _ibcHandler.sendPacket(
                tokenTransferSourcePort,
                _zoneIdToChannel[toZoneId],
                Height.Data({revision_number: 0, revision_height: timeoutHeight}),
                0,
                packet
            );
    }

    /**
     * @dev Fin起点のディスチャージを行うため、対抗チェーンへpacketの送信を行う関数
     *
     * @param accountId 送信元のアカウントID
     * @param fromZoneId ディスチャージ元のゾーンID
     * @param toZoneId ディスチャージ先のゾーンID
     * @param amount ディスチャージする金額
     * @param timeoutHeight packetがタイムアウトするブロック高
     * @param traceId トレースID
     */
    function discharge(
        bytes32 accountId,
        uint16 fromZoneId,
        uint16 toZoneId,
        uint256 amount,
        uint64 timeoutHeight,
        bytes32 traceId
    ) external returns (uint256) {
        require(
            _isNativeRegion(fromZoneId) != _isNativeRegion(toZoneId),
            Error.RV0010_IBC_INVALID_VAL
        );

        require(_isNativeRegion(toZoneId), Error.RV0010_IBC_INVALID_VAL);

        SyncTokenTransferParams memory packetData = SyncTokenTransferParams({
            accountId: accountId,
            fromZoneId: fromZoneId,
            toZoneId: toZoneId,
            amount: amount,
            transferType: Constant.DISCHARGE_FROM_FIN,
            traceId: traceId
        });

        bytes memory packet = abi.encode(packetData);

        return
            _ibcHandler.sendPacket(
                tokenTransferSourcePort,
                _zoneIdToChannel[fromZoneId],
                Height.Data({revision_number: 0, revision_height: timeoutHeight}),
                0,
                packet
            );
    }

    /**
     * @dev relayerからpacketを受け取り、Financial Zoneで管理するBusiness Zoneのステータスを更新する
     *
     * @param packet packet
     */
    function onRecvPacket(Packet.Data calldata packet, address)
        external
        override
        returns (bytes memory acknowledgement)
    {
        require(packet.data.length > 0, Error.RV0010_IBC_INVALID_VAL);

        SyncTokenTransferParams memory packetData;

        packetData = abi.decode(packet.data, (SyncTokenTransferParams));

        if (_isNativeRegion(packetData.toZoneId)) {
            if (packetData.transferType == Constant.DISCHARGE_FROM_FIN) {
                _ibcToken.discharge(
                    packetData.accountId,
                    packetData.fromZoneId,
                    packetData.amount,
                    packetData.traceId
                );
            } else {
                bytes32 ea = _escrowAccounts[packetData.toZoneId][packetData.fromZoneId];
                require(ea != 0x00, Error.GE0110_IBC_APP_JPYTT_ESCROW_NOT_REG);
                _ibcToken.transferFromEscrow(
                    packetData.fromZoneId,
                    ea,
                    ea,
                    packetData.accountId,
                    packetData.amount,
                    packetData.traceId
                );
            }
        } else {
            _ibcToken.issueVoucher(packetData.accountId, packetData.amount, packetData.traceId);
        }

        return bytes("success");
    }

    // /**
    //  * @dev packetの伝播に失敗した場合に、トークンの状態を戻す
    //  *
    //  * @param packet packet
    //  */
    // TODO: packetの伝播失敗をContract単体で検知する仕組みを確立できた後、再度実装する
    // function _refundTokens(bytes memory packet) private {
    //     bytes32 accountId;
    //     uint16 fromZoneId;
    //     uint16 toZoneId;
    //     uint256 amount;
    //     bytes32 traceId;
    //     (accountId, fromZoneId, toZoneId, amount, traceId) = abi.decode(
    //         packet,
    //         (bytes32, uint16, uint16, uint256, bytes32)
    //     );
    //     require(amount != 0, Error.RV0010_IBC_INVALID_VAL);
    //     require(
    //         _isNativeRegion(fromZoneId) != _isNativeRegion(toZoneId),
    //         Error.RV0010_IBC_INVALID_VAL
    //     );

    //     if (_isNativeRegion(fromZoneId)) {
    //         bytes32 ea = _escrowAccounts[fromZoneId][toZoneId];
    //         require(ea != 0x00, Error.IBC_APP_JPYTT_ESCROW_NOT_REG);
    //         _token.transferFromEscrow(
    //             fromZoneId,
    //             ea,
    //             ea,
    //             accountId,
    //             amount,
    //             traceId
    //         );
    //     } else {
    //         _token.issueVoucher(accountId, amount, traceId);
    //     }
    // }

    // /**
    //  * @dev packet受け取り後の処理
    //  *
    //  * @param packet packet
    //  * @param acknowledgement acknowledgement
    //  */
    // function onAcknowledgementPacket(
    //     Packet.Data calldata packet,
    //     bytes calldata acknowledgement,
    //     address
    // ) external virtual override onlyIBC {
    // TODO: packetの伝播失敗をContract単体で検知する仕組みを確立できた後、再度実装する
    // if (keccak256(acknowledgement) != keccak256(bytes("success"))) {
    //     _refundTokens(packet.data);
    // }
    // }

    // /**
    //  * @dev packetがタイムアウトした場合の処理
    //  *
    //  * @param packet packet
    //  */
    // function onTimeoutPacket(Packet.Data calldata packet, address)
    //     external
    //     virtual
    //     override
    //     onlyIBC
    // {
    // TODO: packetの伝播失敗をContract単体で検知する仕組みを確立できた後、再度実装する
    // _refundTokens(packet.data);
    // }

    function onChanOpenInit(IIBCModule.MsgOnChanOpenInit calldata msg_)
        external
        view
        override
        onlyIBC
        returns (string memory)
    {
        require(
            bytes(msg_.version).length == 0 ||
                keccak256(bytes(msg_.version)) == keccak256(bytes(tokenTransferSourceVersion)),
            "version mismatch"
        );
        return tokenTransferSourceVersion;
    }

    function onChanOpenTry(IIBCModule.MsgOnChanOpenTry calldata msg_)
        external
        view
        override
        onlyIBC
        returns (string memory)
    {
        require(
            keccak256(bytes(msg_.counterpartyVersion)) ==
                keccak256(bytes(tokenTransferSourceVersion)),
            "version mismatch"
        );
        return tokenTransferSourceVersion;
    }

    /**
     * @dev リカバリーのためADMIN権限を利用してpacket receiveを再実行する
     *
     * @param packet packet
     */
    function recoverPacket(
        Packet.Data calldata packet,
        address,
        uint256 deadline,
        bytes memory signature
    ) external adminOnly(deadline, signature) {
        this.onRecvPacket(packet, address(0));
    }
}
