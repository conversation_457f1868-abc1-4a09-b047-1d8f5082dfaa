// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IValidator.sol";
import "./interfaces/IValidatorStorage.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/Struct.sol";
import "./interfaces/Error.sol";
import "./libraries/ValidatorLogicCallLib.sol";
import "./libraries/ValidatorLogicExecuteLib.sol";

/**
 * @dev ValidatorLogicコントラクト
 *      外部公開用コントラクト
 */
contract ValidatorLogic is Initializable, IValidator {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev ValidatorStorageアドレス */
    IValidatorStorage private _validatorStorage;

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    /**
     * @dev Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function _adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(
            bytes(err).length == 0 && has,
            bytes(err).length > 0 ? err : Error.GA0020_VALIDATOR_NOT_ADMIN_ROLE
        );
    }

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * @param validatorStorage ValidatorStorageアドレス
     */
    function initialize(IContractManager contractManager, IValidatorStorage validatorStorage)
        public
        initializer
    {
        require(
            address(contractManager) != address(0) && address(validatorStorage) != address(0),
            Error.RV0009_VALIDATOR_INVALID_VAL
        );
        _contractManager = contractManager;
        _validatorStorage = validatorStorage;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @dev Validatorの追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddValidator()
     * ```
     *
     * @param validatorId validatorId
     * @param issuerId issuerId
     * @param name validator名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addValidator(
        bytes32 validatorId,
        bytes32 issuerId,
        bytes32 name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        _adminOnly(
            keccak256(abi.encode(validatorId, issuerId, name, deadline)),
            deadline,
            signature
        );
        ValidatorLogicCallLib.checkAddValidatorIsValid(
            _contractManager,
            _validatorStorage,
            issuerId,
            validatorId
        );
        ValidatorLogicExecuteLib.executeAddValidator(
            _validatorStorage,
            validatorId,
            issuerId,
            name
        );
        emit AddValidator(validatorId, issuerId, name, traceId);
    }

    /**
     * @dev Validator権限の追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddValidatorRole()
     * ```
     *
     * @param validatorId validatorId
     * @param validatorEoa validatorEoa
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addValidatorRole(
        bytes32 validatorId,
        address validatorEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        _adminOnly(keccak256(abi.encode(validatorId, validatorEoa, deadline)), deadline, signature);
        ValidatorLogicCallLib.checkAddValidatorRoleIsValid(
            _validatorStorage,
            validatorId,
            validatorEoa
        );
        ValidatorLogicExecuteLib.executeAddValidatorRole(
            _validatorStorage,
            _contractManager,
            validatorId,
            validatorEoa
        );
        emit AddValidatorRole(validatorId, validatorEoa, traceId);
    }

    /**
     * @dev 検証者の名前を更新する。Adminの権限が必要。
     *
     * ```
     * emit event: ModValidator()
     * ```
     *
     * @param validatorId validatorId
     * @param name validator名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modValidator(
        bytes32 validatorId,
        bytes32 name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        _adminOnly(keccak256(abi.encode(validatorId, name, deadline)), deadline, signature);
        ValidatorLogicCallLib.checkModValidatorIsValid(_validatorStorage, validatorId);
        ValidatorLogicExecuteLib.updateValidatorName(_validatorStorage, validatorId, name);
        emit ModValidator(validatorId, name, traceId);
    }

    /**
     * @dev アカウントの名前を更新する。バリデータの権限が必要。
     *
     * @param validatorId validatorId
     * @param accountId アカウントID
     * @param accountName アカウント名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature バリデータによる署名
     */
    function modAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        bytes32 hash = keccak256(abi.encode(validatorId, accountId, deadline));
        (bool success, string memory err) = this.hasValidatorRole(
            validatorId,
            hash,
            deadline,
            signature
        );
        require(success, err);
        ValidatorLogicCallLib.checkModAccountIsValid(_validatorStorage, validatorId, accountId);
        _contractManager.account().modAccount(accountId, accountName, traceId);
    }

    /**
     * @dev Accountを解約する。
     *
     * ```
     * emit event: SetTerminated()
     * ```
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @param reasonCode 理由コード
     * @param traceId トレースID
     */
    function setTerminated(
        bytes32 validatorId,
        bytes32 accountId,
        bytes32 reasonCode,
        bytes32 traceId
    ) external override {
        ValidatorLogicCallLib.checkValidatorAndAccountExist(
            _validatorStorage,
            validatorId,
            accountId
        );
        _contractManager.account().setTerminated(accountId, reasonCode, traceId);
        (uint16 zoneId, , string memory zoneErr) = _contractManager.provider().getZone();
        ValidatorLogicCallLib.checkZoneHasNoError(zoneErr);
        emit SetTerminated(zoneId, accountId, reasonCode, traceId);
    }

    /**
     * @dev Accountを登録する。
     *
     * ```
     * emit event: AddAccount()
     * ```
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @param accountName account名
     * @param limitValues アカウントの限度額値
     * @param traceId トレースID
     */
    function addAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        AccountLimitValues memory limitValues,
        bytes32 traceId
    ) external override {
        ValidatorLogicCallLib.checkAddAccountIsValid(_validatorStorage, validatorId, accountId);
        ValidatorLogicExecuteLib.executeAddAccountWithLimits(
            _validatorStorage,
            _contractManager,
            validatorId,
            accountId,
            accountName,
            limitValues,
            traceId
        );
        emit AddAccount(validatorId, accountId, false, true, limitValues, traceId);
    }

    /**
     * @dev バリデータが直接管理するアカウントIDを追加する
     *
     * ```
     * emit event: AddValidatorAccountId()
     * ```
     *
     * @param validatorId バリデータID
     * @param accountId アカウントID
     * @param traceId トレースID
     */
    function addValidatorAccountId(
        bytes32 validatorId,
        bytes32 accountId,
        bytes32 traceId
    ) external override {
        ValidatorLogicCallLib.checkAddValidatorAccountIdIsValid(
            _contractManager,
            _validatorStorage,
            validatorId,
            accountId
        );
        ValidatorLogicExecuteLib.updateValidatorAccountId(
            _validatorStorage,
            validatorId,
            accountId
        );
        emit AddValidatorAccountId(validatorId, accountId, traceId);
    }

    /**
     * @dev BusinessZoneAccountを追加する
     *
     * @param validatorId validatorId
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param traceId トレースID
     */
    function setActiveBusinessAccountWithZone(
        bytes32 validatorId,
        uint16 zoneId,
        bytes32 accountId,
        bytes32 traceId
    ) external override {
        ValidatorLogicCallLib.checkValidatorAndAccountExist(
            _validatorStorage,
            validatorId,
            accountId
        );
        _contractManager.businessZoneAccount().setActiveBusinessAccountWithZone(
            zoneId,
            accountId,
            traceId
        );
    }

    /**
     * @dev BusinessZoneアカウント解約
     *
     * ```
     * emit event: SetBizZoneTerminated()
     * ```
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param traceId トレースID
     */
    function setBizZoneTerminated(
        uint16 zoneId,
        bytes32 accountId,
        bytes32 traceId
    ) external override {
        _contractManager.businessZoneAccount().setBizZoneTerminated(zoneId, accountId);
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(accountId);
        emit SetBizZoneTerminated(validatorId, zoneId, accountId, traceId);
    }

    /**
     * @dev BizZone向けAccount登録。(CoreAPI/synchronous用)
     *
     * ```
     * emit event: SyncAccount()
     * ```
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @param accountName accountName
     * @param zoneId zoneId
     * @param zoneName zoneName
     * @param accountStatus 口座のステータス
     * @param reasonCode 理由コード
     * @param approvalAmount 承認額
     * @param traceId トレースID
     */
    function syncAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        uint16 zoneId,
        string memory zoneName,
        bytes32 accountStatus,
        bytes32 reasonCode,
        uint256 approvalAmount,
        bytes32 traceId
    ) external override {
        ValidatorLogicCallLib.checkSyncAccountStatusIsValid(
            _validatorStorage,
            validatorId,
            accountId,
            accountStatus
        );
        ValidatorLogicExecuteLib.executeSyncAccount(
            _validatorStorage,
            _contractManager,
            validatorId,
            accountId,
            accountName,
            accountStatus,
            reasonCode,
            approvalAmount,
            traceId
        );
        emit SyncAccount(
            validatorId,
            accountId,
            accountName,
            zoneId,
            zoneName,
            accountStatus,
            approvalAmount,
            traceId
        );
    }

    /**
     * @dev 指定されたValidatorIdに紐づくValidator情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param validator validator
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setValidatorAll(
        ValidatorAll memory validator,
        uint256 deadline,
        bytes memory signature
    ) external override {
        ValidatorLogicExecuteLib.setValidatorAll(_validatorStorage, validator, deadline, signature);
    }

    /**
     * @dev 検証者IDが登録済であるか確認する。
     *
     * @param validatorId validatorId
     * @return success true:登録済,false:未登録
     * @return err エラーメッセージ
     */
    function hasValidator(bytes32 validatorId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return
            ValidatorLogicCallLib.checkValidatorExists(
                ValidatorLogicCallLib.getValidatorIdExistence(_validatorStorage, validatorId),
                validatorId
            );
    }

    /**
     * @dev 検証者情報リストを取得する。
     *
     * @param limit limit
     * @param offset offset
     * @return validators validators
     * @return totalCount validatorの数
     * @return err エラーメッセージ
     */
    function getValidatorList(uint256 limit, uint256 offset)
        external
        view
        override
        returns (
            ValidatorListData[] memory validators,
            uint256 totalCount,
            string memory err
        )
    {
        return ValidatorLogicCallLib.getValidatorList(_validatorStorage, limit, offset);
    }

    /**
     * @dev Validatorの情報を取得する。
     *
     * @param validatorId validatorId
     * @return name validatorの名前
     * @return issuerId validatorに紐づくissuerId
     * @return err エラーメッセージ
     */
    function getValidator(bytes32 validatorId)
        external
        view
        override
        returns (
            bytes32 name,
            bytes32 issuerId,
            string memory err
        )
    {
        (bool valid, string memory validationErr) = ValidatorLogicCallLib.checkValidatorExists(
            ValidatorLogicCallLib.getValidatorIdExistence(_validatorStorage, validatorId),
            validatorId
        );
        if (!valid) {
            return (bytes32(0), bytes32(0), validationErr);
        }
        ValidatorData memory data = ValidatorLogicCallLib.getValidatorData(
            _validatorStorage,
            validatorId
        );
        return (data.name, data.issuerId, "");
    }

    /**
     * @dev Validatorの総数をカウントする。
     *
     * @return count Validatorの総数
     */
    function getValidatorCount() external view override returns (uint256) {
        return ValidatorLogicCallLib.getValidatorIdsCount(_validatorStorage);
    }

    /**
     * @dev 指定されたValidatorIDにAccountが紐付いているか確認を行う。
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return success true:紐づいている,false:紐づいていない
     * @return err エラーメッセージ
     */
    function hasAccount(bytes32 validatorId, bytes32 accountId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        (bool validValidator, string memory validatorErr) = ValidatorLogicCallLib
            .checkValidatorExists(
                ValidatorLogicCallLib.getValidatorIdExistence(_validatorStorage, validatorId),
                validatorId
            );
        if (!validValidator) return (false, validatorErr);
        return
            ValidatorLogicCallLib.checkAccountExists(
                ValidatorLogicCallLib.getAccountIdExistenceByValidatorId(
                    _validatorStorage,
                    validatorId,
                    accountId
                ),
                accountId
            );
    }

    /**
     * @dev バリデータ権限を持っているか確認する。
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return success true:権限あり,false:権限なし
     */
    function hasValidatorByAccount(bytes32 validatorId, bytes32 accountId)
        external
        view
        override
        returns (bool)
    {
        return
            ValidatorLogicCallLib.getAccountIdExistenceByValidatorId(
                _validatorStorage,
                validatorId,
                accountId
            );
    }

    /**
     * @dev Validatorに紐づくAccountの情報を取得する。
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return accountData アカウントデータ(zoneIdなし)
     * @return err エラーメッセージ
     */
    function getAccount(bytes32 validatorId, bytes32 accountId)
        external
        view
        override
        returns (AccountDataWithLimitData memory accountData, string memory err)
    {
        return
            ValidatorLogicCallLib.getAccountWithLimits(
                _validatorStorage,
                _contractManager,
                validatorId,
                accountId
            );
    }

    /**
     * @dev バリデータが直接管理するアカウントIDを取得する
     *
     * @param validatorId バリデータID
     * @return accountId アカウントID
     * @return err エラー
     */
    function getValidatorAccountId(bytes32 validatorId)
        external
        view
        override
        returns (bytes32 accountId, string memory err)
    {
        (bool valid, string memory validationErr) = ValidatorLogicCallLib.checkValidatorExists(
            ValidatorLogicCallLib.getValidatorIdExistence(_validatorStorage, validatorId),
            validatorId
        );
        if (!valid) return (bytes32(0), validationErr);

        ValidatorData memory data = ValidatorLogicCallLib.getValidatorData(
            _validatorStorage,
            validatorId
        );
        (bool validAccount, string memory accountErr) = ValidatorLogicCallLib
            .checkValidatorAccountIdExists(data.validatorAccountId);
        if (!validAccount) return (bytes32(0), accountErr);
        return (data.validatorAccountId, "");
    }

    /**
     * @dev Indexに応じたValidatorIdを返す。
     *
     * @param index index
     * @return validatorId validatorId
     * @return err エラーメッセージ
     */
    function getValidatorId(uint256 index)
        external
        view
        override
        returns (bytes32 validatorId, string memory err)
    {
        return
            index >= ValidatorLogicCallLib.getValidatorIdsCount(_validatorStorage)
                ? (bytes32(0), Error.UE0110_VALIDATOR_OUT_OF_INDEX)
                : (ValidatorLogicCallLib.getValidatorIdByIndex(_validatorStorage, index), "");
    }

    /**
     * @dev (CoreBatch専用 BCClient経由)該当ValidatorIDに紐づくAccountの情報を取得する。
     * @dev レスポンスのValidatorAccountsDataのソート順について
     * @dev validatorMapping[validatorId].accountIdsの格納順の逆順となる。
     *
     * @param validatorId validatorId
     * @param offset オフセット
     * @param limit リミット
     * @return accounts ValidatorAccountsData[]
     * @return totalCount
     * @return err エラーメッセージ
     */
    function getAccountAllList(
        bytes32 validatorId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        override
        returns (
            ValidatorAccountsDataALL[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        return
            ValidatorLogicCallLib.getValidatorAccountAllList(
                _validatorStorage,
                _contractManager,
                validatorId,
                offset,
                limit
            );
    }

    /**
     * @dev 該当ValidatorIDに紐づくAccountの情報を取得する。
     *
     * @param validatorId validatorId
     * @param offset オフセット
     * @param limit リミット
     * @param sortOrder ソート順(desc: 降順, asc: 昇順)
     * @return accounts ValidatorAccountsData[]
     * @return totalCount
     * @return err エラーメッセージ
     */
    function getAccountList(
        bytes32 validatorId,
        uint256 offset,
        uint256 limit,
        string memory sortOrder
    )
        external
        view
        override
        returns (
            ValidatorAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        return
            ValidatorLogicCallLib.getValidatorAccountList(
                _validatorStorage,
                _contractManager,
                validatorId,
                offset,
                limit,
                sortOrder
            );
    }

    /**
     * @dev アカウントに連携済みのzone情報一覧を取得する。(finZone含む)
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return zones zoneIdの配列
     * @return err エラーメッセージ
     */
    function getZoneByAccountId(bytes32 validatorId, bytes32 accountId)
        external
        view
        override
        returns (ZoneData[] memory zones, string memory err)
    {
        (bool hasAccountSuccess, string memory accountErr) = ValidatorLogicCallLib
            .checkAccountExists(
                ValidatorLogicCallLib.getAccountIdExistenceByValidatorId(
                    _validatorStorage,
                    validatorId,
                    accountId
                ),
                accountId
            );
        if (!hasAccountSuccess) return (new ZoneData[](0), accountErr);

        (uint16 zoneId, , string memory zoneErr) = _contractManager.provider().getZone();
        ValidatorLogicCallLib.checkZoneHasNoError(zoneErr);
        return
            ValidatorLogicCallLib.getZonesForFinancialAccount(_contractManager, accountId, zoneId);
    }

    /**
     * @dev バリデータ権限を持っているか確認する。
     *
     * @param validatorId validatorId
     * @param hash hash
     * @param deadline deadline
     * @param signature signature
     * @return success true:権限あり,false:権限なし
     * @return err エラーメッセージ
     */
    function hasValidatorRole(
        bytes32 validatorId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view override returns (bool success, string memory err) {
        (bool validInput, string memory validationErr) = ValidatorLogicCallLib
            .checkValidatorIdIsValid(validatorId);
        if (!validInput) return (false, validationErr);
        return
            _contractManager.accessCtrl().checkRole(
                ValidatorLogicCallLib.getValidatorData(_validatorStorage, validatorId).role,
                hash,
                deadline,
                signature
            );
    }

    /**
     * @dev バックアップ用に検証者データを取得する。
     *
     * @param index index
     * @return validator ValidatorAll構造体
     */
    function getValidatorAll(uint256 index) external view override returns (ValidatorAll memory) {
        return ValidatorLogicCallLib.getValidatorAll(_validatorStorage, index);
    }

    /**
     * @dev 移転先のアカウント情報を取得する
     *
     * @param accountId アカウントID
     * @return accountName アカウント名
     * @return err エラー
     */
    function getDestinationAccount(bytes32 accountId)
        external
        view
        returns (string memory accountName, string memory err)
    {
        return _contractManager.account().getDestinationAccount(accountId);
    }

    /**
     * @dev Validatorに紐づくAccountの全情報を取得する。(接続済みBusinessZoneAccount情報含む)
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return accountDataAll アカウントデータ(zoneIdあり)
     * @return err エラーメッセージ
     */
    function getAccountAll(bytes32 validatorId, bytes32 accountId)
        external
        view
        returns (AccountDataAll memory accountDataAll, string memory err)
    {
        (bool hasValidatorSuccess, string memory validatorErr) = ValidatorLogicCallLib
            .checkValidatorExists(
                ValidatorLogicCallLib.getValidatorIdExistence(_validatorStorage, validatorId),
                validatorId
            );
        if (!hasValidatorSuccess) {
            return (accountDataAll, validatorErr);
        }

        (bool hasAccountSuccess, string memory accountErr) = ValidatorLogicCallLib
            .checkAccountExists(
                ValidatorLogicCallLib.getAccountIdExistenceByValidatorId(
                    _validatorStorage,
                    validatorId,
                    accountId
                ),
                accountId
            );
        if (!hasAccountSuccess) {
            return (accountDataAll, accountErr);
        }
        return _contractManager.account().getAccountAll(accountId);
    }
}
