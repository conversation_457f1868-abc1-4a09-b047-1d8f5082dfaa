// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "../interfaces/IValidator.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev Validatorインターフェース。
 *      トークンの転送を許可された権限。
 *      ブロックチェーンのvalidatorとは関係が無いことに注意すること。
 *      何かを検証するわけではないが「検証者」とする。
 */
contract ValidatorMock is IValidator {
    bytes32 private constant _STATUS_APPLYING = "applying";
    bytes32 private constant STATUS_ACTIVE = "active";
    bytes32 private constant STATUS_TERMINATING = "terminating";
    bytes32 private constant STATUS_TERMINATED = "terminated";

    /** @dev AccountData */
    struct MockAccountData {
        string accountName;
        bytes32 accountStatus;
    }

    /** @dev BusinessZoneAccountData */
    struct MockBusiessZoneAccountData {
        bytes32 accountStatus;
    }

    /** @dev AccountData */
    mapping(bytes32 => MockAccountData) private _accountData;
    /** @dev BusinessZoneAccountData */
    mapping(uint16 => mapping(bytes32 => MockBusiessZoneAccountData))
        private _businessZoneAccountData;
    /* @dev finZoneid */
    uint16 private constant _finRegionId = 3000;
    /* @dev bizZoneId */
    uint16 private constant _bizRegionId = 3001;

    ///////////////////////////////////
    // Mock functions
    ///////////////////////////////////

    // テスト用
    function getBizZoneAccount(uint16 fromZoneId, bytes32 accountId)
        external
        view
        returns (bytes32)
    {
        return _businessZoneAccountData[fromZoneId][accountId].accountStatus;
    }

    ///////////////////////////////////
    // Override functions
    ///////////////////////////////////

    function addValidator(
        bytes32,
        bytes32,
        bytes32,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function addValidatorRole(
        bytes32,
        address,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function modValidator(
        bytes32,
        bytes32,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function modAccount(
        bytes32,
        bytes32,
        string memory,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    // function modAccountLimit(
    //     bytes32,
    //     bytes32,
    //     uint256,
    //     uint256,
    //     uint256,
    //     uint256,
    //     uint256,
    //     bytes32,
    //     uint256,
    //     bytes memory
    // ) external pure override {
    //     revert("unused override only");
    // }

    function setTerminated(
        bytes32,
        bytes32,
        bytes32,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function setBizZoneTerminated(
        uint16,
        bytes32,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function addAccount(
        bytes32,
        bytes32,
        string memory,
        AccountLimitValues memory,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function addValidatorAccountId(
        bytes32,
        bytes32,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function setActiveBusinessAccountWithZone(
        bytes32,
        uint16,
        bytes32,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function syncAccount(
        bytes32,
        bytes32 accountId,
        string memory,
        uint16,
        string memory,
        bytes32 accountStatus,
        bytes32,
        uint256,
        bytes32
    ) external override {
        _accountData[accountId].accountName;

        if (accountStatus == _STATUS_APPLYING) {
            _accountData[accountId].accountStatus = STATUS_ACTIVE;
        } else if (accountStatus == STATUS_TERMINATING) {
            _accountData[accountId].accountStatus = STATUS_TERMINATED;
        }
    }

    function hasValidator(bytes32) external pure override returns (bool, string memory) {
        revert("unused override only");
    }

    function getValidatorList(uint256, uint256)
        external
        pure
        override
        returns (
            ValidatorListData[] memory,
            uint256,
            string memory
        )
    {
        revert("unused override only");
    }

    function getValidator(bytes32)
        external
        pure
        override
        returns (
            bytes32,
            bytes32,
            string memory
        )
    {
        revert("unused override only");
    }

    function getValidatorCount() external pure override returns (uint256) {
        revert("unused override only");
    }

    function hasAccount(bytes32, bytes32) external pure override returns (bool, string memory) {
        return (false, "");
    }

    function hasValidatorByAccount(bytes32, bytes32) external pure override returns (bool) {
        revert("unused override only");
    }

    function getAccount(bytes32, bytes32 accountId)
        external
        view
        override
        returns (AccountDataWithLimitData memory accountData, string memory err)
    {
        CumulativeTransactionLimits memory emptyLimits = CumulativeTransactionLimits({
            cumulativeMintLimit: 0,
            cumulativeMintAmount: 0,
            cumulativeBurnLimit: 0,
            cumulativeBurnAmount: 0,
            cumulativeChargeLimit: 0,
            cumulativeChargeAmount: 0,
            cumulativeDischargeLimit: 0,
            cumulativeDischargeAmount: 0,
            cumulativeTransferLimit: 0,
            cumulativeTransferAmount: 0
        });
        AccountDataWithLimitData memory accountDataWithLimitData = AccountDataWithLimitData({
            accountName: _accountData[accountId].accountName,
            accountStatus: _accountData[accountId].accountStatus,
            balance: 0, // モックなので仮の値を返す
            reasonCode: 0, // モックなので仮の値を返す
            appliedAt: 0, // モックなので仮の値を返す
            registeredAt: 0, // モックなので仮の値を返す
            terminatingAt: 0,
            terminatedAt: 0,
            mintLimit: 0,
            burnLimit: 0,
            chargeLimit: 0,
            dischargeLimit: 0,
            transferLimit: 0,
            cumulativeLimit: 0,
            cumulativeAmount: 0,
            cumulativeDate: 0,
            cumulativeTransactionLimits: emptyLimits
        });
        return (
            accountDataWithLimitData, // モックなので仮の値を返す
            "" // モックなので仮の値を返す
        );
    }

    function getValidatorAccountId(bytes32)
        external
        pure
        override
        returns (bytes32, string memory)
    {
        revert("unused override only");
    }

    function getValidatorId(uint256) external pure override returns (bytes32, string memory) {
        revert("unused override only");
    }

    function getAccountList(
        bytes32,
        uint256,
        uint256,
        string memory
    )
        external
        pure
        override
        returns (
            ValidatorAccountsData[] memory,
            uint256,
            string memory
        )
    {
        revert("unused override only");
    }

    function getAccountAllList(
        bytes32,
        uint256,
        uint256
    )
        external
        pure
        override
        returns (
            ValidatorAccountsDataALL[] memory,
            uint256,
            string memory
        )
    {
        revert("unused override only");
    }

    function getZoneByAccountId(bytes32, bytes32)
        external
        pure
        override
        returns (ZoneData[] memory, string memory)
    {
        revert("unused override only");
    }

    function hasValidatorRole(
        bytes32,
        bytes32,
        uint256,
        bytes memory
    ) external pure override returns (bool, string memory) {
        revert("unused override only");
    }

    function getValidatorAll(uint256) external pure override returns (ValidatorAll memory) {
        revert("unused override only");
    }

    function setValidatorAll(
        ValidatorAll memory,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }
}
