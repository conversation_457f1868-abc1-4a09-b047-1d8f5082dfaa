// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "../interfaces/IToken.sol";
import "../interfaces/Struct.sol";

contract TokenMock is IToken {
    bytes32 private constant STATUS_ACTIVE = "active";

    ///////////////////////////////////
    // Mock functions
    ///////////////////////////////////

    function addToken(
        bytes32,
        bytes32,
        bytes32,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function modToken(
        bytes32,
        bytes32,
        bytes32,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function setTokenEnabled(
        bytes32,
        bytes32,
        bool,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function approve(
        bytes32,
        bytes32,
        bytes32,
        uint256,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function getAllowance(
        bytes32,
        bytes32,
        bytes32
    )
        external
        pure
        override
        returns (
            uint256,
            uint256,
            string memory
        )
    {
        revert("unused override only");
    }

    function getAllowanceList(
        bytes32,
        uint256,
        uint256
    )
        external
        pure
        override
        returns (
            AccountApprovalAll[] memory,
            uint256,
            string memory
        )
    {
        revert("unused override only");
    }

    function mint(
        bytes32,
        bytes32,
        uint256,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function burn(
        bytes32,
        bytes32,
        uint256,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function burnCancel(
        bytes32,
        bytes32,
        uint256,
        uint256,
        bytes32,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function transferSingle(
        bytes32,
        bytes32,
        bytes32,
        uint256,
        bytes32,
        string memory,
        string memory,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function addTotalSupply(uint256) external pure override {
        revert("unused override only");
    }

    function subTotalSupply(uint256) external pure override {
        revert("unused override only");
    }

    function setTokenAll(
        TokenAll memory,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    function hasToken(bytes32, bool) external pure override returns (bool, string memory) {
        revert("unused override only");
    }

    function hasTokenState() external view returns (bool, string memory) {
        revert("unused override only");
    }

    function getToken()
        external
        pure
        override
        returns (
            bytes32,
            bytes32,
            bytes32,
            uint256,
            bool,
            string memory
        )
    {
        revert("unused override only");
    }

    function getTokenAll() external pure override returns (TokenAll memory) {
        revert("unused override only");
    }
}
