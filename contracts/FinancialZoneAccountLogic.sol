// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./interfaces/Error.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/IFinancialZoneAccountStorage.sol";
import "./interfaces/Struct.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import {IFinancialZoneAccount} from "./interfaces/IFinancialZoneAccount.sol";

import "./libraries/FinancialZoneAccountLogicCallLib.sol";
import "./libraries/FinancialZoneAccountLogicExecuteLib.sol";

contract FinancialZoneAccountLogic is Initializable, IFinancialZoneAccount {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレ */
    IContractManager private _contractManager;

    /** @dev FinancialZoneAccountStorageアドレス */
    IFinancialZoneAccountStorage private _financialZoneAccountStorage;

    /** @dev 1日あたりの秒数 */
    uint256 private constant _DAILY_TIME = 86400;
    /** @dev UTCとJSTの時差 */
    uint256 private constant _JST_TIME_DIFFERENCE = 32400;

    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant _EMPTY_LENGTH = 0;
    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant _GET_FINACCOUNTS_LIMIT = 1000;
    /** @dev 各種限度額のチェック対象(mint) **/
    bytes32 private constant _CHECK_MINT = "checkMint";
    /** @dev 各種限度額のチェック対象(burn) **/
    bytes32 private constant _CHECK_BURN = "checkBurn";
    /** @dev 各種限度額のチェック対象(charge) **/
    bytes32 private constant _CHECK_CHARGE = "checkCharge";
    /** @dev 各種限度額のチェック対象(discharge) **/
    bytes32 private constant _CHECK_DISCHARGE = "checkDischarge";
    /** @dev 各種限度額のチェック対象(transfer) **/
    bytes32 private constant _CHECK_TRANSFER = "checkTransfer";
    /** @dev getFinAccountsAllのsignature検証用 **/
    string private constant _GET_FINACCOUNTS_ALL_SIGNATURE = "getFinAccountsAll";
    /* @dev setBizAccountsAllのsignature検証用 */
    string private constant _SET_FINACCOUNTS_ALL_SIGNATURE = "setFinAccountsAll";

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager ContractManagerアドレス
     * @param financialZoneAccountStorage FinancialZoneAccountStorageアドレス
     */
    function initialize(
        IContractManager contractManager,
        IFinancialZoneAccountStorage financialZoneAccountStorage
    ) public initializer {
        require(
            address(contractManager) != address(0) &&
                address(financialZoneAccountStorage) != address(0),
            Error.RV0009_FINANCIAL_ZONE_ACCOUNT_INVALID_VAL
        );
        _contractManager = contractManager;
        _financialZoneAccountStorage = financialZoneAccountStorage;
    }

    /**
     * @dev コントラクトバージョン取得。
     * @return version コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev アカウント限度額追加 TODO:CoreAPIマッピングとの整合性確認時に作成
     *
     * @param accountId アカウントID
     * @param limitValues アカウントの限度額値
     * @param traceId トレースID
     */
    function addAccountLimit(
        bytes32 accountId,
        AccountLimitValues memory limitValues,
        bytes32 traceId
    ) external override {
        FinancialZoneAccountLogicCallLib.checkAddAccountLimitDataIsValid(limitValues);
        FinancialZoneAccountLogicExecuteLib.addAccountLimitData(
            _financialZoneAccountStorage,
            accountId,
            limitValues
        );

        emit AddAccountLimit(accountId, limitValues, traceId);
    }

    /**
     * @dev アカウント限度額更新(Issuerコントラクト側で判定処理は完了している前提)
     *
     * @param accountId アカウントID
     * @param limitUpdates アカウント限度額の更新フラグ
     * @param limitValues アカウントの限度額値
     */
    function modAccountLimit(
        bytes32 accountId,
        AccountLimitUpdates memory limitUpdates,
        AccountLimitValues memory limitValues
    ) external returns (AccountLimitValues memory) {
        // Issuer専用関数
        FinancialZoneAccountLogicCallLib.checkSenderIsIssuer(_contractManager);
        return
            FinancialZoneAccountLogicExecuteLib.modAccountLimitData(
                _financialZoneAccountStorage,
                accountId,
                limitUpdates,
                limitValues
            );
    }

    /**
     * @dev Account累積限度額初期化(Issuerコントラクト側で判定処理は完了している前提)
     *
     * @param accountId アカウントID
     */
    function cumulativeReset(bytes32 accountId) external {
        // Issuer専用関数
        FinancialZoneAccountLogicCallLib.checkSenderIsIssuer(_contractManager);
        FinancialZoneAccountLogicExecuteLib.resetCumulative(
            _financialZoneAccountStorage,
            accountId,
            this.getJSTDay()
        );
    }

    /**
     * @dev cumulative amount初期化
     *
     * @param accountId アカウントID
     * @param traceId トレースID
     */
    function syncCumulativeReset(bytes32 accountId, bytes32 traceId) external override {
        // accountId存在チェック
        FinancialZoneAccountLogicCallLib.checkHasAccount(_contractManager, accountId);

        FinancialZoneAccountLogicExecuteLib.syncCumulativeReset(
            _financialZoneAccountStorage,
            accountId,
            this.getJSTDay()
        );

        emit SyncCumulativeReset(accountId, traceId);
    }

    /**
     * @dev 累積限度額更新 TODO calcにするかaddにするか
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @param traceId トレースID
     * @return cumulativeDate 累積限度額更新日時
     * @return cumulativeAmount 累積限度額
     */
    function addCumlativeAmount(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override returns (uint256 cumulativeDate, uint256 cumulativeAmount) {
        // Token専用関数 TODO 呼び出し元の確認
        FinancialZoneAccountLogicCallLib.checkSenderIsToken(_contractManager);

        emit AddCumulativeAmount(
            accountId,
            amount,
            FinancialZoneAccountLogicCallLib.getCumulativeDate(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            traceId
        );

        return (
            FinancialZoneAccountLogicExecuteLib.addAmount(
                _financialZoneAccountStorage,
                accountId,
                amount,
                this.getJSTDay()
            )
        );
    }

    /**
     * @dev 累積限度額減額
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @param traceId トレースID
     * @return cumulativeDate 累積限度額更新日時
     * @return cumulativeAmount 累積限度額
     */
    function subtractCumulativeAmount(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override returns (uint256 cumulativeDate, uint256 cumulativeAmount) {
        // Token専用関数 TODO 呼び出し元の確認
        FinancialZoneAccountLogicCallLib.checkSenderIsToken(_contractManager);

        emit SubtractCumulativeAmount(
            accountId,
            amount,
            FinancialZoneAccountLogicCallLib.getCumulativeDate(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            traceId
        );

        return (
            FinancialZoneAccountLogicExecuteLib.subtractAmount(
                _financialZoneAccountStorage,
                accountId,
                amount
            )
        );
    }

    /**
     * @dev mintされた額のamountを限度額に反映させる
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @param traceId トレースID
     */
    function syncMint(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        FinancialZoneAccountLogicExecuteLib.syncLimitAmount(
            _financialZoneAccountStorage,
            accountId,
            amount,
            this.getJSTDay(),
            Constant.SYNC_MINT
        );

        emit SyncMint(
            accountId,
            amount,
            FinancialZoneAccountLogicCallLib.getCumulativeDate(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeMintAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            traceId
        );
    }

    /**
     * @dev burnされた額のamountを限度額に反映させる
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @param traceId トレースID
     */
    function syncBurn(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        FinancialZoneAccountLogicExecuteLib.syncLimitAmount(
            _financialZoneAccountStorage,
            accountId,
            amount,
            this.getJSTDay(),
            Constant.SYNC_BURN
        );

        emit SyncBurn(
            accountId,
            amount,
            FinancialZoneAccountLogicCallLib.getCumulativeDate(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeBurnAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            traceId
        );
    }

    /**
     * @dev chargeされた額のamountを限度額に反映させる
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @param traceId トレースID
     */
    function syncCharge(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        // syncChargeでは、packet伝播後の対抗チェーン側でのrevert発生を回避するため限度額チェックは行わない
        FinancialZoneAccountLogicExecuteLib.syncLimitAmount(
            _financialZoneAccountStorage,
            accountId,
            amount,
            this.getJSTDay(),
            Constant.SYNC_CHARGE
        );
        emit SyncCharge(
            accountId,
            amount,
            FinancialZoneAccountLogicCallLib.getCumulativeDate(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeChargeAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            traceId
        );
    }

    /**
     * @dev dischargeされた額のamountを限度額に反映させる
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @param traceId トレースID
     */
    function syncDischarge(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        // syncDischargeでは、packet伝播後の対抗チェーン側でのrevert発生を回避するため限度額チェックは行わない
        FinancialZoneAccountLogicExecuteLib.syncLimitAmount(
            _financialZoneAccountStorage,
            accountId,
            amount,
            this.getJSTDay(),
            Constant.SYNC_DISCHARGE
        );

        emit SyncDischarge(
            accountId,
            amount,
            FinancialZoneAccountLogicCallLib.getCumulativeDate(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeDischargeAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            traceId
        );
    }

    /**
     * @dev tranferされた額のamountを限度額に反映させる
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @param traceId トレースID
     */
    function syncTransfer(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        FinancialZoneAccountLogicExecuteLib.syncLimitAmount(
            _financialZoneAccountStorage,
            accountId,
            amount,
            this.getJSTDay(),
            Constant.SYNC_TRANSFER
        );

        emit SyncTransfer(
            accountId,
            amount,
            FinancialZoneAccountLogicCallLib.getCumulativeDate(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            FinancialZoneAccountLogicCallLib.getCumulativeTransferAmount(
                _financialZoneAccountStorage,
                accountId
            ),
            traceId
        );
    }

    /**
     * @dev 指定されたAcountIdsに紐づくFinancialZoneAccounts情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param finAccount finAccountsInfo
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setFinAccountAll(
        FinancialZoneAccountsAll memory finAccount,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        bytes32 hash = keccak256(abi.encode(_SET_FINACCOUNTS_ALL_SIGNATURE, deadline));
        FinancialZoneAccountLogicCallLib.checkRoleAccountAdmin(
            _contractManager,
            hash,
            deadline,
            signature
        );
        FinancialZoneAccountLogicExecuteLib.setFinAccountAll(
            _financialZoneAccountStorage,
            finAccount
        );
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev 限度額データ存在確認
     *
     * @param accountId アカウントID
     * @return success true:OK, false:NG
     * @return err エラーメッセージ
     */
    function hasAccount(bytes32 accountId) external pure returns (bool success, string memory err) {
        return FinancialZoneAccountLogicCallLib.hasAccount(accountId);
    }

    /**
     * @dev アカウントの限度額を取得する(stack too deep回避のためreturnをオブジェクト化する)
     *
     * @param accountId accountId
     * @return accountLimitData
     * @return err
     */
    function getAccountLimitData(bytes32 accountId)
        external
        view
        override
        returns (FinancialZoneAccountData memory accountLimitData, string memory err)
    {
        // Accountコントラクト、もしくはFinancialCheckコントラクトのみから呼び出し可能
        FinancialZoneAccountLogicCallLib.checkSenderIsTokenOrFinancial(_contractManager);

        // AccountのgetAccountLimitはValidatorコントラクトのgetAccountLimitから呼び出している
        // validatorIdとaccountIdの存在確認はValidatorコントラクトで確認済み
        return (
            FinancialZoneAccountLogicCallLib.getAccountLimitData(
                _financialZoneAccountStorage,
                accountId
            ),
            ""
        );
    }

    /**
     * @dev UTCからJSTの0時へ変換
     *
     */
    function getJSTDay() external view override returns (uint256) {
        // (((blockTimestamp + 3600秒 * 9) / 3600秒 * 24) * 3600秒 * 24) - 3600秒 * 9
        return
            (((block.timestamp + _JST_TIME_DIFFERENCE) / _DAILY_TIME) * _DAILY_TIME) -
            _JST_TIME_DIFFERENCE;
    }

    /**
     * @dev タイムスタンプをJSTの0時へ変換
     *
     * @param timestamp タイムスタンプ
     */
    function convertJSTDay(uint256 timestamp) external pure override returns (uint256) {
        // (((timestamp + 3600秒 * 9) / 3600秒 * 24) * 3600秒 * 24) - 3600秒 * 9
        return
            (((timestamp + _JST_TIME_DIFFERENCE) / _DAILY_TIME) * _DAILY_TIME) -
            _JST_TIME_DIFFERENCE;
    }

    /**
     * @dev 1日の限度額チェック
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkTransactionLimits(
        bytes32 accountId,
        uint256 amount,
        bytes32 operationType
    ) external view override returns (bool success, string memory err) {
        return
            FinancialZoneAccountLogicCallLib.checkTransactionLimits(
                _financialZoneAccountStorage,
                accountId,
                amount,
                operationType,
                this.getJSTDay()
            );
    }

    /**
     * @dev 発行限度額チェック
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkMint(bytes32 accountId, uint256 amount)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return
            FinancialZoneAccountLogicCallLib.checkMint(
                _financialZoneAccountStorage,
                _contractManager,
                accountId,
                amount,
                this.getJSTDay()
            );
    }

    /**
     * @dev 償却限度額チェック
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkBurn(bytes32 accountId, uint256 amount)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return
            FinancialZoneAccountLogicCallLib.checkBurn(
                _financialZoneAccountStorage,
                _contractManager,
                accountId,
                amount,
                this.getJSTDay()
            );
    }

    /**
     * @dev チャージ限度額チェック
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkCharge(bytes32 accountId, uint256 amount)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return
            FinancialZoneAccountLogicCallLib.checkCharge(
                _financialZoneAccountStorage,
                _contractManager,
                accountId,
                amount,
                this.getJSTDay()
            );
    }

    /**
     * @dev @dev ディスチャージ限度額チェック
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkDischarge(bytes32 accountId, uint256 amount)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return
            FinancialZoneAccountLogicCallLib.checkDischarge(
                _financialZoneAccountStorage,
                _contractManager,
                accountId,
                amount,
                this.getJSTDay()
            );
    }

    /**
     * @dev 移転限度額チェック
     *
     * @param accountId アカウントID
     * @param amount 金額
     * @return success true:OK / false:NG
     * @return err エラーメッセージ
     */
    function checkTransfer(bytes32 accountId, uint256 amount)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return
            FinancialZoneAccountLogicCallLib.checkTransfer(
                _financialZoneAccountStorage,
                _contractManager,
                accountId,
                amount,
                this.getJSTDay()
            );
    }

    /**
     * @dev limitとoffsetで指定したFinancialZoneAccountsを一括取得する
     *
     */
    function getFinAccountAll(uint256 index)
        external
        view
        override
        returns (FinancialZoneAccountsAll memory finAccounts)
    {
        return
            FinancialZoneAccountLogicCallLib.getFinAccountAll(_financialZoneAccountStorage, index);
    }
}
