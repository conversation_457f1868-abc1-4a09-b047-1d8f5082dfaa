// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

/**
 * @dev Error文字列
 */
library Error {
    // solhint-disable state-visibility

    //
    // 01000～01999: AccessCtrl
    //
    // string constant ACTRL_BEGIN = "1000";
    /// @dev Error: 署名タイムアウト
    string constant GA0001_ACTRL_SIG_TIMEOUT = "GA0001:sig timeout";
    /// @dev Error: roleが不正
    string constant RV0001_ACTRL_BAD_ROLE = "RV0001:bad role";
    /// @dev Error: 署名が不正
    string constant GS0001_ACTRL_BAD_SIG = "GS0001:bad sig";
    //
    /// @dev Error: 処理を実行する権限が無い
    string constant GA0002_ACTRL_NOT_ROLE = "GA0002:not role";
    /// @dev Error: 値が不正
    string constant RV0002_ACTRL_INVALID_VAL = "RV0002:accessctrl invalid value";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant GA0003_ACTRL_NOT_ADMIN_ROLE = "GA0003:accessctrl not admin";
    //
    // string constant ACTRL_END = "1999";

    //
    // 02000～02999: Provider
    //
    // string constant PROV_BEGIN = "2000";
    /// @dev Error: 登録済みプロバイダID
    string constant GE1001_PROV_ID_EXIST = "GE1001:exist providerID";
    /// @dev Error: 未登録プロバイダID
    string constant GE0101_PROV_ID_NOT_EXIST = "GE0101:not exist";
    /// @dev Error: 登録済みアドレス
    string constant GE1002_PROV_ADDR_EXIST = "GE1002:exist addr";
    /// @dev Error: 無効プロバイダ
    string constant GE2001_PROV_DISABLED = "GE2001:disabled";
    /// @dev Error: 登録済みcurrID
    string constant GE1003_PROV_CURRID_EXIST = "GE1003:exist currID";
    /// @dev Error: ProviderIDではない
    string constant GE2002_NOT_PROVIDER_ID = "GE2002:not provider id";
    //
    /// @dev Error: 処理を実行する権限が無い
    string constant GA0004_PROV_NOT_ROLE = "GA0004:not prov";
    /// @dev Error: プロバイダ未登録
    string constant GE0102_PROV_NOT_EXIST = "GE0102:provider not exist";
    /// @dev Error: ゾーン未登録
    string constant GE0103_ZONE_NOT_EXIST = "GE0103:zone not exist";
    /// @dev Error: 呼び出し元がProviderのコントラクトではない
    string constant GA0005_NOT_PROVIDER_CONTRACT = "GA0005:not provider contract";
    /// @dev Error: 値が不正
    string constant RV0003_PROV_INVALID_VAL = "RV0003:provider invalid value";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant GA0006_PROV_NOT_ADMIN_ROLE = "GA0006:provider not admin";
    //
    // string constant PROV_END = "2999";

    //
    // 03000～03999: Issuer
    //
    // string constant ISSUER_BEGIN = "3000";
    /// @dev Error: 登録済み発行者ID
    string constant GE1004_ISSUER_ID_EXIST = "GE1004:exist id";
    /// @dev Error: 未登録発行者ID
    string constant GE0104_ISSUER_ID_NOT_EXIST = "GE0104:not exist";
    /// @dev Error: 登録済みアドレス
    string constant GE1005_ISSUER_ADDR_EXIST = "GE1005:exist addr";
    /// @dev Error: 無効発行者
    string constant GE2003_ISSUER_DISABLED = "GE2003:disabled";
    /// @dev Error: 登録済みユーザID
    string constant GE1006_ISSUER_ACCOUNT_EXIST = "GE1006:exist userID";
    /// @dev Error: 登録済みトークンID
    string constant GE1007_ISSUER_TOKEN_EXIST = "GE1007:exist tokenID";
    /// @dev Error: 登録済みAccountID
    string constant GE1008_ISSUER_HAS_THIS_ACCOUNT_ID = "GE1008:exist accountID";
    /// @dev Error: itemFlgs配列の要素数が不正
    string constant RV0004_ITEMFLGS_INVALID_VAL = "RV0004:itemFlgs invalid count";
    /// @dev Error: limitAmount配列の要素数が不正
    string constant RV0005_LIMITAMOUNT_INVALID_VAL = "RV0005:limitAmount invalid count";
    /// @dev Error: 同一のbankCodeが既にとうろくされている
    string constant GE1009_ISSUER_EXIST_BANK_CODE = "GE1009:exist bank code";
    //
    /// @dev Error: 処理を実行する権限が無い
    string constant GA0007_ISSUER_NOT_ROLE = "GA0007:not issuer";
    /// @dev Error: 範囲外を指定された
    string constant UE0101_ISSUER_OUT_OF_INDEX = "UE0101:out of index";
    /// @dev Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された
    string constant UE0102_ISSUER_TOO_LARGE_LIMIT = "UE0102:too large limit";
    /// @dev Error: 一覧検索時のoffsetとして範囲外を指定された
    string constant UE0103_ISSUER_OFFSET_OUT_OF_INDEX = "UE0103:out of index (offset)";
    /// @dev Error: 呼び出し元がIssuerのコントラクトではない
    string constant GA0008_NOT_ISSUER_CONTRACT = "GA0008:not issuer contract";
    /// @dev Error: 値が不正
    string constant RV0006_ISSUER_INVALID_VAL = "RV0006:issuer invalid value";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant GA0009_ISSUER_NOT_ADMIN_ROLE = "GA0009:issuer not admin";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant GE4201_ISSUER_EXCEED_REGISTER_LIMIT = "GE4201:exceed issuer register limit";
    /// @dev Error: 発行者がDecurretでない
    string constant GE2004_ISSUER_NOT_DECURRET = "GE2004:issuer not decurret";

    //
    // string constant ISSUER_END = "3999";

    //
    // 06000～06999: Account
    //
    // string constant ACCOUNT_BEGIN = "6000";
    /// @dev Error: 登録済みアカウントID
    string constant GE1010_ACCOUNT_ID_EXIST = "GE1010:exist id";
    /// @dev Error: 未登録アカウントID
    string constant GE0105_ACCOUNT_ID_NOT_EXIST = "GE0105:not exist";
    /// @dev Error: 登録済みアドレス
    string constant GE1011_ACCOUNT_ADDR_EXIST = "GE1011:exist addr";
    /// @dev Error: 無効アカウント
    string constant GE2005_ACCOUNT_DISABLED = "GE2005:disabled";
    /// @dev Error: ownerId未登録 approve用
    string constant UE0104_OWNER_NOT_EXIST = "UE0104:owner not exist";
    /// @dev Error: spenerId未登録 approve用
    string constant UE0105_SPENDER_NOT_EXIST = "UE0105:spender not exist";
    /// @dev Error: Allowance額が送金額に届いていない際のエラー
    string constant UE4401_ALLOWANCE_NOT_ENOUGH = "UE4401:allowance not enough";
    /// @dev Error: balance残高不足
    string constant UE4402_BALANCE_NOT_ENOUGH = "UE4402:balance not enough";
    /// @dev Error: 本人確認登録なし
    string constant GE2006_ACCOUNT_NOT_IDENTIFIED = "GE2006:account not identified";
    /// @dev Error: 解約済アカウント
    string constant GE2007_ACCOUNT_TERMINATED = "GE2007:terminated account";
    /// @dev Error: 解約時に残高あり
    string constant UE2001_ACCOUNT_BALANCE_EXIST = "UE2001:balance exist account";
    /// @dev Error: アカウント解約時にアカウントが凍結状態でない
    string constant GE2016_ACCOUNT_NOT_FROZEN = "GE2016:Account not frozen";
    /// @dev Error: アカウント解約時にアカウントの残高が0でない
    string constant GE2017_ACCOUNT_BALANCE_NOT_ZERO = "GE2017:Account balance not zero";
    /// @dev Error: アカウントステータスが強制償却済みでない
    string constant GE2018_ACCOUNT_NOT_FORCE_BURNED = "GE2018:Account not force burned";
    /// @dev Error: 許可額の設定値が上限を超えている
    string constant GE4203_ACCOUNT_EXCEED_APPROVAL_LIMIT = "GE4203:Exceed approval limit";
    /// @dev Error: アカウントは既に解約申し込み中か、解約済み
    string constant GE2019_ACCOUNT_TERMINATING_OR_TERINATED =
        "GE2019:Account already terminating or terminated";
    /// @dev Error: アカウントステータスが凍結状態、強制償却済みでない
    string constant GE2020_ACCOUNT_NOT_FROZEN_OR_FORCE_BURNED =
        "GE2020:Account not frozen or force burned";
    /// @dev Error: 強制償却時にアカウントの残高が足りない
    string constant UE4404_ACCOUNT_INVALID_BURNED_AMOUNT = "UE4404:Invalid burned amount";
    /// @dev Error: 強制償却時にアカウントの残高が指定した償却額と一致しない
    string constant UE4405_ACCOUNT_INVALID_BURNED_BALANCE = "UE4405:Invalid burned balance";
    /// @dev Error: 送信元アカウントIDが不正
    string constant RV0018_SEND_ACCOUNT_IS_INVALID_VALUE = "RV0018:Send account is invalid value";
    /// @dev Error: 送金元アカウントIDが不正
    string constant RV0019_FROM_ACCOUNT_IS_INVALID_VALUE = "RV0019:From account is invalid value";
    /// @dev Error: 送金先アカウントIDが不正
    string constant RV0020_TO_ACCOUNT_IS_INVALID_VALUE = "RV0020:To account is invalid value";
    /// @dev Error: 送信元アカウントIDが未登録
    string constant GE0114_SEND_ACCOUNT_IS_NOT_EXIST = "GE0114:Send account does not exist";
    /// @dev Error: 送金元アカウントIDが未登録
    string constant GE0115_FROM_ACCOUNT_IS_NOT_EXIST = "GE0115:From account does not exist";
    /// @dev Error: 送金先アカウントIDが未登録
    string constant GE0116_TO_ACCOUNT_IS_NOT_EXIST = "GE0116:To account does not exist";
    /// @dev Error: 送信元アカウントIDが無効
    string constant GE2021_SEND_ACCOUNT_STATUS_IS_DISABLED =
        "GE2021:Send account status is disabled";
    /// @dev Error: 送金元アカウントIDが無効
    string constant GE2022_FROM_ACCOUNT_STATUS_IS_DISABLED =
        "GE2022:From account status is disabled";
    /// @dev Error: 送金先アカウントIDが無効
    string constant GE2023_TO_ACCOUNT_STATUS_IS_DISABLED = "GE2023:To account status is disabled";
    /// @dev Error: 送金元と送信先アカウントのイシュアーが異なる
    string constant UE2007_FROM_AND_TO_ACCOUNT_ISSUERS_ARE_DIFFERENT =
        "UE2007:From and to account issuers are different";

    //
    /// @dev Error: 処理を実行する権限が無い
    string constant GA0010_ACCOUNT_NOT_ROLE = "GA0010:not user";
    /// @dev Error: 範囲外を指定された
    string constant UE0106_ACCOUNT_OUT_OF_INDEX = "UE0106:out of index";
    /// @dev Error: 処理を実行する権限が無い
    string constant GA0011_ACCOUNT_PROV_NOT_ROLE = "GA0011:not prov";
    /// @dev Error: 呼び出し元がAccountコントラクトではない
    string constant GA0012_NOT_ACCOUNT_CONTRACT = "GA0012:not account contract";
    /// @dev Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された
    string constant UE0107_ACCOUNT_TOO_LARGE_LIMIT = "UE0107:too large limit";
    /// @dev Error: 一覧検索時のoffsetとして範囲外を指定された
    string constant UE0108_ACCOUNT_OFFSET_OUT_OF_INDEX = "UE0108:out of index (offset)";
    /// @dev Error: 値が不正
    string constant RV0007_ACCOUNT_INVALID_VAL = "RV0007:account invalid value";
    // Note: overflow/underflowは0.8.xで言語仕様により検知されるが、互換性のためエラーコードを維持
    /// @dev Error: 加算オーバーフロー
    string constant SR0001_ACCOUNT_OVERFLOW = "SR0001:account overflow";
    /// @dev Error: 減算オーバーフロー
    string constant SR0002_ACCOUNT_UNDERFLOW = "SR0002:account underflow";
    /// @dev Error: アカウント登録限界数
    string constant GE4202_ACCOUNT_EXCEED_REGISTER_LIMIT = "GE4202:exceed account register limit";
    /// @dev Error: Invalid account signature.
    string constant GS0002_ACCOUNT_BAD_SIG = "GS0002:bad account sig";
    /// @dev Error: Invalid signature parameter.
    string constant GS0003_ACCOUNT_INVALID_SIG = "GS0003:bad account sig";
    /// @dev Error: Adminでない
    string constant GA0029_ACCOUNT_NOT_ADMIN = "GA0029:Account not admin";
    /// @dev Error: Accountのstatusが不正
    string constant RV0021_ACCOUNT_STATUS_INVALID = "RV0021:Account status is invalid";
    //
    // string constant ACCOUNT_END = "6999";

    //
    // 07000～07999: Token
    //
    // string constant TOKEN_BEGIN = "7000";
    /// @dev Error: 登録済みトークンID
    string constant GE1012_TOKEN_ID_EXIST = "GE1012:exist token id";
    /// @dev Error: 未登録トークンID
    string constant GE0106_TOKEN_ID_NOT_EXIST = "GE0106:token id not exist";
    /// @dev Error: 登録済みアドレス
    string constant GE1013_TOKEN_ADDR_EXIST = "GE1013:exist token addr";
    /// @dev Error: 無効トークン
    string constant GE2008_TOKEN_DISABLED = "GE2008:token disabled";
    /// @dev Error: balance残高不足
    string constant UE4403_TOKEN_BALANCE_NOT_ENOUGH = "UE4403:balance not enough";
    /// @dev Error: Tokenでの無効ユーザ
    string constant UE2002_TOKEN_ACCOUNT_DISABLED = "UE2002:disabled account";
    /// @dev Error: Tokenでの本人未確認ユーザ
    string constant UE2003_TOKEN_ACCOUNT_UNIDENTIFIED = "UE2003:not identified account";
    /// @dev Error: approve関係の失敗
    string constant UE2004_TOKEN_APPROVE = "UE2004:approve";
    /// @dev Error: トークンIDの発行者ではない
    string constant GE2009_TOKEN_ISSUER_UNKNOWN = "GE2009:unknown";
    /// @dev Error: 登録されているトークンIDではない
    string constant GE2010_NOT_TOKEN_ID = "GE2010:not token id";
    /// @dev Error: 登録されているトークンIDではない
    string constant GE0107_TOKEN_NOT_EXIST = "GE0107:token not exist";
    /// @dev Error: 送金金額が0
    string constant RV0022_TOKEN_ZERO_AMOUNT = "RV0022:Token zero amount";
    //
    /// @dev Error: 処理を実行する権限が無い
    string constant GA0013_TOKEN_NOT_ROLE = "GA0013:not token";
    /// @dev Error: 範囲外を指定された
    string constant UE0109_TOKEN_OUT_OF_INDEX = "UE0109:out of index";
    /// @dev Error: 処理を実行する権限が無い(issuer)
    string constant GA0014_TOKEN_ISSUER_NOT_ROLE = "GA0014:not issuer";
    /// @dev Error: 処理を実行する権限が無い(currency)
    string constant GA0015_TOKEN_CURR_NOT_ROLE = "GA0015:not currency";
    /// @dev Error: 呼び出し元がTokenコントラクトではない
    string constant GA0016_NOT_TOKEN_CONTRACT = "GA0016:not token contract";
    //
    /// @dev Error: 値が不正
    string constant RV0008_TOKEN_INVALID_VAL = "RV0008:token invalid value";
    // Note: overflow/underflowは0.8.xで言語仕様により検知されるが、互換性のためエラーコードを維持
    /// @dev Error: 加算オーバーフロー
    string constant SR0003_TOKEN_OVERFLOW = "SR0003:token overflow";
    /// @dev Error: 減算オーバーフロー
    string constant SR0004_TOKEN_UNDERFLOW = "SR0004:token underflow";

    //
    // string constant TOKEN_END = "7999";

    //
    // 08000～08999: Validator
    //
    // string constant VALID_BEGIN = "8000";
    /// @dev Error: 登録済み検証者ID
    string constant GE1014_VALIDATOR_ID_EXIST = "GE1014:exist id";
    /// @dev Error: 未登録検証者ID
    string constant GE0108_VALIDATOR_ID_NOT_EXIST = "GE0108:not exist";
    /// @dev Error: 登録済みアドレス
    string constant GE1015_VALIDATOR_ADDR_EXIST = "GE1015:exist addr";
    /// @dev Error: 無効検証者
    string constant GE2011_VALIDATOR_DISABLED = "GE2011:disabled";
    /// @dev Error: 検証者管理のアカウントIDが未登録
    string constant GE0109_VALIDATOR_ACCOUNT_NOT_EXIST = "GE0109:validator account not exist";
    //
    /// @dev Error: 処理を実行する権限が無い
    string constant GA0018_VALIDATOR_NOT_ROLE = "GA0018:not valid";
    /// @dev Error: 範囲外を指定された
    string constant UE0110_VALIDATOR_OUT_OF_INDEX = "UE0110:out of index";
    /// @dev Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された
    string constant UE0111_VALIDATOR_TOO_LARGE_LIMIT = "UE0111:too large limit";
    /// @dev Error: 一覧検索時のoffsetとして範囲外を指定された
    string constant UE0112_VALIDATOR_OFFSET_OUT_OF_INDEX = "UE0112:out of index (offset)";
    /// @dev Error: 呼び出し元がValidatorのコントラクトではない
    string constant GA0019_NOT_VALIDATOR_CONTRACT = "GA0019:not validator contract";
    /// @dev Error: 値が不正
    string constant RV0009_VALIDATOR_INVALID_VAL = "RV0009:validator invalid value";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant GA0020_VALIDATOR_NOT_ADMIN_ROLE = "GA0020:validator not admin";
    /// @dev Error: FromAccountとToAccountが同じ。
    string constant UE2005_FROM_TO_SAME = "UE2005:from to are same";

    //
    // string constant VALID_END = "8999";

    //
    // 09000～09999: IBC
    //
    // string constant IBC_BEGIN = "9000";
    /// @dev Error: msg.senderが許可されていない
    string constant GA0021_IBC_SENDER_NOT_AUTH = "GA0021:sender not authorized";
    /// @dev Error: パケットがタイムアウトした
    string constant GE2012_IBC_PACKET_TIMED_OUT = "GE2012:packet timed out";
    /// @dev Error: パケットはすでに受信している
    string constant GE2013_IBC_PACKET_ALWAYS_RECV = "GE2013:packet always received";
    /// @dev Error: msg.senderが許可されていない
    string constant GA0022_NOT_IBC_CONTRACT = "GA0022:not ibc contract";
    //
    /// @dev Error: エスクローアカウントが登録されていない
    string constant GE0110_IBC_APP_JPYTT_ESCROW_NOT_REG = "GE0110:escrow not registered";
    /// @dev Error: エスクローアカウントがすでに登録されている
    string constant GE1016_IBC_APP_JPYTT_ESCROW_ALWAYS_REG = "GE1016:escrow always registered";
    /// @dev Error: 外部コントラクトのエラー
    string constant SR0005_IBC_APP_JPYTT_EXTERNAL_CONTRACT_ERR = "SR0005:external contract error";
    /// @dev Error: 値が不正
    string constant RV0010_IBC_INVALID_VAL = "RV0010:IBC invalid value";
    /// @dev Error: 署名復元したアドレスがAdmin権限を持たない(hash不一致の可能性もある)
    string constant GA0023_IBC_NOT_ADMIN_ROLE = "GA0023:IBC not admin";
    /// @dev Error: 操作ゾーンが不適切
    string constant GA0031_FIN_ZONE_OP_NOT_ALLOWED = "GA0031:Fin zone operation denied";
    /// @dev Error: パケット送信元のゾーンが不適切
    string constant GA0032_NOT_ALLOWED_FROM_FIN_ZONE = "GA0032:Not allowed from financial zone";
    /// @dev Error: 値が不正
    string constant RV0009_FINANCIAL_ZONE_ACCOUNT_INVALID_VAL =
        "RV0009:financial zone account invalid value";

    //
    // string constant VALID_END = "9999";

    // 10000～10999: TransferProxy
    //
    // string constant TRANSFERPROXY_BEGIN = "10000";
    /// @dev Error: CustomContractのAddressが未登録である
    string constant GE0111_CUSTOM_CONTRACT_NOT_EXIST = "GE0111:custom contract not exist";
    /// @dev Error: CustomContractが既に登録されている
    string constant GE1017_CUSTOM_CONTRACT_EXIST = "GE1017:custom contract already exist";
    //
    // string constant TRANSFERPROXY_END = "10999";

    // 11000～11999: FinancialToken (将来: エラーコード整理対象)
    //
    // string constant FINANCIAL_BEGIN = "11000";
    /// @dev Error: 値が不正
    string constant RV0011_REGION_INVALID_VAL = "RV0011:region invalid val";
    /// @dev Error: FinancialAccountコントラクトからの呼び出しではない
    string constant GA0024_NOT_FINANCIAL_ACCOUNT = "GA0024:not financial account";
    /// @dev Error: FinancialTokenコントラクトからの呼び出しではない
    string constant GA0025_NOT_FINANCIAL_TOKEN = "GA0025:not financial token";
    /// @dev Error: limitAmountsが要素数設定されていない
    string constant RV0012_LIMIT_AMOUNT_INVALID_COUNT = "RV0012:limitAmount invalid count";
    /// @dev Error: 付加領域からの呼び出しではない
    string constant GA0026_NOT_INDUSTRY_REGION = "GA0026:not industry region";
    /// @dev Error: DAILYの上限を超えている。
    string constant UE4101_EXCEEDED_DAILY_LIMIT = "UE4101:exceeded daily limit";
    /// @dev Error: MINTの上限を超えている。
    string constant UE4001_EXCEEDED_MINT_LIMIT = "UE4001:exceeded mint limit";
    /// @dev Error: BURNの上限を超えている。
    string constant UE4002_EXCEEDED_BURN_LIMIT = "UE4002:exceeded burn limit";
    /// @dev Error: TRANSFERの上限を超えている。
    string constant UE4003_EXCEEDED_TRANSFER_LIMIT = "UE4003:exceeded transfer limit";
    /// @dev Error: CHARGEの上限を超えている。
    string constant UE4004_EXCEEDED_CHARGE_LIMIT = "UE4004:exceeded charge limit";
    /// @dev Error: DISCHARGEの上限を超えている。
    string constant UE4005_EXCEEDED_DISCHARGE_LIMIT = "UE4005:Exceeded discharge limit";
    /// @dev Error: MINT DAILYの上限を超えている。
    string constant UE4102_EXCEEDED_DAILY_MINT_LIMIT = "UE4102:Exceeded daily mint limit";
    /// @dev Error: BURN DAILYの上限を超えている。
    string constant UE4103_EXCEEDED_DAILY_BURN_LIMIT = "UE4103:Exceeded daily burn limit";
    /// @dev Error: CHARGE DAILYの上限を超えている。
    string constant UE4104_EXCEEDED_DAILY_CHARGE_LIMIT = "UE4104:Exceeded daily charge limit";
    /// @dev Error: DISCHARGE DAILYの上限を超えている。
    string constant UE4105_EXCEEDED_DAILY_DISCHARGE_LIMIT = "UE4105:Exceeded daily discharge limit";
    /// @dev Error: TRANSFER DAILYの上限を超えている。
    string constant UE4106_EXCEEDED_DAILY_TRANSFER_LIMIT = "UE4106:Exceeded daily transfer limit";

    // 12000～12999: RenewableEnergyToken
    //
    // string constant RenewableEnergyToken_BEGIN = "12000";
    /// @dev Error: 一覧検索時のlimitとして最大取得件数を超える値が指定された
    string constant UE0113_RETOKEN_TOO_LARGE_LIMIT = "UE0113:retoken too large limit";
    /// @dev Error: 一覧検索時のoffsetとして範囲外を指定された
    string constant UE0114_RETOKEN_OFFSET_OUT_OF_INDEX = "UE0114:retoken out of index (offset)";
    /// @dev Error: Token登録限界数
    string constant GE4204_RETOKEN_EXCEED_REGISTER_LIMIT = "GE4204:exceed token register limit";
    /// @dev Error: Tokenが存在しない
    string constant GE0112_RETOKEN_NOT_EXIST = "GE0112:retoken not exist";
    /// @dev Error: Tokenを所有していない
    string constant GA0028_RETOKEN_NOT_OWNER = "GA0028:retoken not owner";
    /// @dev Error: TokenのステータスがActiveでない
    string constant GE2014_RETOKEN_NOT_ACTIVE = "GE2014:retoken not active";
    /// @dev Error: Tokenがロックされている
    string constant GE2015_RETOKEN_IS_LOCKED = "GE2015:retoken is locked";
    /// @dev Error: FromAccouontIdとToAccountIdが同一
    string constant UE2006_RETOKEN_FROM_TO_ARE_SAME = "UE2006:retoken from to are same";
    /// @dev Error: 値が不正
    string constant RV0015_RETOKEN_INVALID_VAL = "RV0015:retoken invalid val";
    /// @dev Error: すでにTokenが登録済み
    string constant GE1019_RETOKEN_ID_EXIST = "GE1019:retoken id exist";
    /// @dev Error: 不正なmiscValue1
    string constant RV0026_RETOKEN_INVALID_MISC1 = "RV0026:Invalid misc value 1";
    /// @dev Error: 不正なmiscValue2
    string constant RV0027_RETOKEN_INVALID_MISC2 = "RV0027:Invalid misc value 2";
    // string constant RenewableEnergyToken_END = "12999";

    // 20000~20999: 汎用的なバリデーションエラー
    /// @dev Error: organizationId(issuerId/validatorId)が無効な値
    string constant RV0016_INVALID_ORGANIZATION_ID = "RV0016:organization id is invalid";
    /// @dev Error: accountIdが無効な値
    string constant RV0017_INVALID_ACCOUNT_ID = "RV0017:account id is invalid";
    /// @dev Error: コントラクトアドレスが未登録
    string constant GE0113_CONTRACT_ADDRESS_NOT_EXIST = "GE0113:contract address not exist";
    /// @dev Error: コントラクトアドレスが不正
    string constant GE0114_BUSINESS_ZONE_ACCOUNT_INVALID_VAL =
        "GE0114:business zone account invalid value";
    /// @dev Error: 呼び出し元のコントラクトが誤り
    string constant GA0030_INVALID_CALLER_ADDRESS = "GA0030:Caller is different";
    /// @dev Error: 呼び出し元がオーナーではない
    string constant GA0027_RESTRICTED_TO_OWNER = "GA0027:restricted to owner";
    // solhint-enable state-visibility
}
