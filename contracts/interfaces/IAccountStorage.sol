// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev AccountStorageインターフェース
 *      Accountデータのストレージ操作を定義
 *      AccountLogicコントラクトからのみ呼び出し可能
 */
interface IAccountStorage {
    ///////////////////////////////////
    // AccountData CRUD操作
    ///////////////////////////////////

    /**
     * @dev アカウントの登録
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountName アカウント名
     */
    function addAccountData(bytes32 accountId, string memory accountName) external;

    /**
     * @dev アカウントIDを指定してvalidatorIdを取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return validatorId バリデータID
     */
    function getValidatorId(bytes32 accountId) external view returns (bytes32 validatorId);

    /**
     * @dev アカウントに紐づくvalidatorIdの登録
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param validatorId バリデータID
     */
    function addValidatorId(bytes32 accountId, bytes32 validatorId) external;

    /**
     * @dev アカウント名取得
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountName アカウント名
     */
    function getAccountName(bytes32 accountId) external view returns (string memory accountName);

    /**
     * @dev アカウント名の変更
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountName アカウント名
     */

    function updateAccountName(bytes32 accountId, string memory accountName) external;

    /**
     * @dev カウントの残高取得
     * @param accountId マッピングのキーとなるアカウントID
     * @return balance アカウントの残高
     */
    function getAccountBalance(bytes32 accountId) external view returns (uint256 balance);

    /**
     * @dev accountの残高更新
     * @param accountId マッピングのキーとなるアカウントID
     * @param balance 指定の残高
     */
    function setAccountBalance(bytes32 accountId, uint256 balance) external;

    ///////////////////////////////////
    // AccountIds配列操作
    ///////////////////////////////////

    /**
     * @dev アカウントID登録
     * @param accountId マッピングのキーとなる追加対象アカウントID
     */
    function addAccountId(bytes32 accountId) external;

    /**
     * @dev アカウントのステータスを解約済みに更新する　TODO:他関数と統合する
     * @param accountId マッピングのキーとなるアカウントID
     * @param reasonCode 理由コード
     */
    function setTerminated(bytes32 accountId, bytes32 reasonCode) external;

    /**
     * @dev 連携済みzone情報の追加
     * @param accountId マッピングのキーとなるアカウントID
     * @param zoneId zoneId
     */
    function addZone(bytes32 accountId, uint16 zoneId) external;

    ///////////////////////////////////
    // AccountIdExistence マッピング操作
    ///////////////////////////////////

    /**
     * @dev accountの存在フラグを取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return exists 存在フラグ
     */
    function getAccountIdExistence(bytes32 accountId) external view returns (bool exists);

    /**
     * @dev accountの存在フラグを設定する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param exists 存在フラグ
     */
    function addAccountIdExistence(bytes32 accountId, bool exists) external;

    /**
     * @dev アカウントステータス情報を取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountStatus アカウントステータス
     */
    function getAccountStatus(bytes32 accountId) external view returns (bytes32 accountStatus);

    /**
     * @dev Accountの状態を更新する(凍結 or アクティブ)
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     * @param reasonCode 理由コード
     */
    function setAccountStatusAndReasonCode(
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode
    ) external;

    /**
     * @dev accountの状態を更新する(アクティブ)
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     */
    function setAccountStatus(bytes32 accountId, bytes32 accountStatus) external;

    /**
     * @dev 送金許可設定。
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param approvedAt 支払い許可日時
     * @param amount 許容額
     */
    function setApprove(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 approvedAt,
        uint256 amount
    ) external;

    ///////////////////////////////////
    // バックアップ・リストア関連
    ///////////////////////////////////

    /**
     * @dev バックアップ用に全発行者データを設定する（Admin権限必要）
     * @param account 全発行者データ
     */
    function setAccountAll(AccountsAll memory account) external;

    /**
     * @dev Accountの情報を返す。
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountDataWithoutZoneId アカウントデータ(zoneIdなし)
     */
    function getAccountDataWithoutZoneId(bytes32 accountId)
        external
        view
        returns (AccountDataWithoutZoneId memory accountDataWithoutZoneId);

    /**
     * @dev account dataを返す。
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountData アカウントデータ
     */
    function getAccountData(bytes32 accountId)
        external
        view
        returns (AccountData memory accountData);

    /**
     * @dev IndexよりAccountIDを取得する。
     *
     * @param index index
     * @return accountId accountId
     * @return err エラーメッセージ
     */
    function getAccountId(uint256 index)
        external
        view
        returns (bytes32 accountId, string memory err);

    /**
     * @dev アカウントの許可額を取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param index 許可対象のアカウントID
     * @return allowance 許可額
     * @return approvedAt 許可日付
     */
    function getAllowance(bytes32 accountId, bytes32 index)
        external
        view
        returns (uint256 allowance, uint256 approvedAt);

    /**
     * @dev 送金許可一覧照会 TODO:Core APIとのマッピング時に作成
     * @param ownerId 送金許可元ID
     * @param offset オフセット
     * @param limit リミット
     * @return approvalData 送金許可設定一覧
     * @return totalCount 総数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        bytes32 ownerId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        );

    /**
     * @dev Accountの数を返却する。
     * @return count accountの数
     */
    function getAccountCount() external view returns (uint256 count);

    /**
     * @dev アカウントに連携済みのzoneIdの取得
     * @param accountId マッピングのキーとなるアカウントID
     * @return zoneIdList アカウントに連携済みのzoneIdのリスト
     */
    function getAccountZoneIdList(bytes32 accountId)
        external
        view
        returns (uint16[] memory zoneIdList);

    /**
     * @dev indexよりaccount詳細情報を取得する
     *
     * @param index マッピングのキーとなるアカウントID
     * @return account アカウント全ての情報
     */
    function getAccountAll(uint256 index) external view returns (AccountsAll memory account);

    /**
     * @dev 送金許可額の減額を行う。
     * @param ownerId ownerId
     * @param spenderId 送金許可対象者のID
     * @param amount 送金額
     */
    function setAllowance(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external;
}
