// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./RenewableEnergyTokenStruct.sol";

/**
 * @dev RenewableEnergyTokenStorageインターフェース
 *      RenewableEnergyTokenデータのストレージ操作を定義
 *      RenewableEnergyTokenLogicコントラクトからのみ呼び出し可能
 */
interface IRenewableEnergyTokenStorage {
    ///////////////////////////////////
    // RenewableEnergyTokenData CRUD操作
    ///////////////////////////////////

    /**
     * @dev トークンの詳細情報を取得する
     *
     * @param tokenId トークンID
     * @return renewableEnergyTokenData トークンデータ
     */
    function getTokenById(bytes32 tokenId) external view returns (RenewableEnergyTokenData memory);

    /**
     * @dev 指定されたtokenIdの所有者と前の所有者を更新する
     * @param fromAccountId 移転元アカウントID
     * @param toAccountId 移転先アカウントID
     * @param tokenId トークンID
     */
    function setNewAccountIdForToken(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 tokenId
    ) external;

    /**
     * @dev mint関数のtokenId配列にtokenIdを追加する
     *
     * @param tokenId トークンID
     */
    function addTokenId(bytes32 tokenId) external;

    /**
     * @dev mint関数でaccountIdにtokenIdを追加する
     * @param accountId アカウントID
     * @param tokenId トークンID
     */
    function addTokenIdToAccountId(bytes32 accountId, bytes32 tokenId) external;

    /**
     * @dev トークンを追加する
     * @param tokenId トークンID
     * @param metadataId メタデータID
     * @param metadataHash メタデータハッシュ
     * @param mintAccountId 発行するアカウントID
     * @param ownerAccountId 所有者のアカウントID
     * @param isLocked トークンのロック状態
     */
    function addToken(
        bytes32 tokenId,
        bytes32 metadataId,
        bytes32 metadataHash,
        bytes32 mintAccountId,
        bytes32 ownerAccountId,
        bool isLocked
    ) external;

    /**
     * @dev 移転元アカウントの該当トークン所有情報を削除する
     * @param accountId 移転もとのアカウントID
     * @param tokenId キーとなるトークンID
     */
    function removeTokenIdFromAccountId(bytes32 accountId, bytes32 tokenId) external;

    /**
     * @dev 指定されたtokenIdに紐づくToken情報を登録、もしくは上書きする
     * @param token Tokenの情報
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setRenewableEnergyTokenAll(
        RenewableEnergyTokenAll memory token,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev 指定されたaccountIdに紐づくTokenIdを登録、もしくは上書きする
     * @param tokenIdByAccountId accountIdに紐づくTokenId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setTokenIdsByAccountIdAll(
        TokenIdsByAccountIdAll memory tokenIdByAccountId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev トークンの詳細情報を取得する
     *
     * @param tokenId トークンID
     * @return renewableEnergyTokenData トークンデータ
     * @return err エラーメッセージ
     */
    function getToken(bytes32 tokenId)
        external
        view
        returns (RenewableEnergyTokenData memory renewableEnergyTokenData, string memory err);

    /**
     * @dev Tokenの数を返却する。
     *
     * @return count tokenの数
     */
    function getTokenCount() external view returns (uint256 count);

    /**
     * @dev 指定されたインデックスに対応するToken IDを返却する。
     *
     * @param index 取得したいTokenのインデックス
     * @return tokenId 対応するTokenのID
     */
    function getTokenIdsByIndex(uint256 index) external view returns (bytes32);

    /**
     * @dev 指定されたインデックスのRenewableEnergyTokenAll情報を取得する
     * @param index 取得tokensのindex
     */
    function getRenewableEnergyTokenAll(uint256 index)
        external
        view
        returns (RenewableEnergyTokenAll memory renewableEnergyToken);

    /**
     * @dev 指定されたaccountIdに紐づくTokenIdを取得
     * @param accountId accountId
     * @return tokenIdsByAccountId accountIdに紐づくTokenIdのリスト
     */
    function getTokenIdsByAccountIdAll(bytes32 accountId)
        external
        view
        returns (TokenIdsByAccountIdAll memory tokenIdsByAccountId);

    /**
     * @dev RenewableEnergyTokenLogicアドレスを更新する（Admin権限必要）
     * @param renewableEnergyTokenLogicAddr 新しいRenewableEnergyTokenLogicアドレス
     */
    function setRenewableEnergyTokenLogicAddress(address renewableEnergyTokenLogicAddr) external;

    /**
     * @dev RenewableEnergyTokenLogicアドレスを取得する
     * @return renewableEnergyTokenLogicAddr AccountLogicアドレス
     */
    function getRenewableEnergyTokenLogicAddress() external view returns (address);

    /**
     * @dev LogicコントラクトのアドレスをContractManagerアドレスに変更する（Admin権限必要）
     * @param contractManagerAddr 新しいContractManagerアドレス
     */
    function setContractManagerAddress(address contractManagerAddr) external;

    /**
     * @dev ContractManagerアドレスを取得する
     * @return contractManagerAddr ContractManagerアドレス
     */
    function getContractManagerAddress() external view returns (address contractManagerAddr);
}
