// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev FinancialZoneAccountStorageインターフェース
 *      FinancialZoneAccountデータのストレージ操作を定義
 *      FinancialZoneAccountLogicコントラクトからのみ呼び出し可能
 */
interface IFinancialZoneAccountStorage {
    /**
     * @dev 発行者データを取得する
     * @param accountId マッピングのキーとなるアカウントID
     * @return financialZoneAccountData 発行者データ
     */
    function getFinancialZoneAccountData(bytes32 accountId)
        external
        view
        returns (FinancialZoneAccountData memory financialZoneAccountData);

    /**
     * @dev 発行者データを設定する
     * @param accountId マッピングのキーとなるアカウントID
     * @param financialZoneAccountData 発行者データ
     */
    function setFinancialZoneAccountData(
        bytes32 accountId,
        FinancialZoneAccountData memory financialZoneAccountData
    ) external;

    /**
     * @dev 発行者データを取得する
     * @param accountId マッピングのキーとなるアカウントID
     * @return cumulativeTransactionLimits 発行者データ
     */
    function getCumulativeTransactionLimits(bytes32 accountId)
        external
        view
        returns (CumulativeTransactionLimits memory cumulativeTransactionLimits);

    /**
     * @dev 発行者データを設定する
     * @param accountId マッピングのキーとなるアカウントID
     * @param cumulativeTransactionLimits 発行者データ
     */
    function setCumulativeTransactionLimits(
        bytes32 accountId,
        CumulativeTransactionLimits memory cumulativeTransactionLimits
    ) external;

    /**
     * @dev アカウント限度額登録
     * @param accountId マッピングのキーとなるアカウントID
     * @param limitValues アカウントの限度額値
     */
    function addAccountLimitData(bytes32 accountId, AccountLimitValues memory limitValues) external;

    /**
     * @dev cumulative amount初期化
     * @param accountId マッピングのキーとなるアカウントID
     * @param jstDay 現在の日付
     */
    function resetCumulative(bytes32 accountId, uint256 jstDay) external;

    /**
     * @dev cumulative amount更新
     * @param accountId マッピングのキーとなるアカウントID
     */
    function syncCumulativeReset(bytes32 accountId, uint256 JSTDay) external;

    /**
     * @dev cumulativeDate
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeDate(bytes32 accountId) external view returns (uint256 cumulativeDate);

    /**
     * @dev cumulativeAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeAmount(bytes32 accountId)
        external
        view
        returns (uint256 cumulativeAmount);

    /**
     * @dev cumulativeMintAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeMintAmount(bytes32 accountId)
        external
        view
        returns (uint256 cumulativeMintAmount);

    /**
     * @dev cumulativeBurnAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeBurnAmount(bytes32 accountId)
        external
        view
        returns (uint256 cumulativeBurnAmount);

    /**
     * @dev cumulativeChargeAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeChargeAmount(bytes32 accountId)
        external
        view
        returns (uint256 cumulativeChargeAmount);

    /**
     * @dev cumulativeDischargeAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeDischargeAmount(bytes32 accountId)
        external
        view
        returns (uint256 cumulativeAmount);

    /**
     * @dev cumulativeTransferAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeTransferAmount(bytes32 accountId)
        external
        view
        returns (uint256 cumulativeTransferAmount);

    /**
     * @dev 指定されたAcountIdsに紐づくFinancialZoneAccounts情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     * @param finAccount finAccountsInfo
     */
    function setFinAccountAll(FinancialZoneAccountsAll memory finAccount) external;

    /**
     * @dev limitとoffsetで指定したFinancialZoneAccountsを一括取得する
     * @param index index
     */
    function getFinAccountAll(uint256 index)
        external
        view
        returns (FinancialZoneAccountsAll memory finAccounts);

    /**
     * @dev ContractManagerアドレスを更新する（Admin権限必要）
     * @param contractManagerAddr 新しいContractManagerアドレス
     */
    function setContractManagerAddress(address contractManagerAddr) external;

    /**
     * @dev ContractManagerアドレスを取得する
     * @return contractManagerAddr ContractManagerアドレス
     */
    function getContractManagerAddress() external view returns (address contractManagerAddr);

    /**
     * @dev FinancialZoneAccountLogicアドレスを更新する（Admin権限必要）
     * @param financialZoneAccountLogicAddr 新しいFinancialZoneAccountLogicアドレス
     */
    function setFinancialZoneAccountLogicAddress(address financialZoneAccountLogicAddr) external;

    /**
     * @dev FinancialZoneAccountLogicアドレスを取得する
     * @return FinancialZoneAccountLogicアドレス
     */
    function getFinancialZoneAccountLogicAddress() external view returns (address);
}
