// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev IBusinessZoneAccountStorageインターフェース
 *      BusinessZoneAccountデータのストレージ操作を定義
 *      BusinessZoneAccountLogicコントラクトからのみ呼び出し可能
 */
interface IBusinessZoneAccountStorage {
    ///////////////////////////////////
    // BusinessZoneAccountData CRUD操作
    ///////////////////////////////////

    /**
     * @dev BusinessZoneAccountデータを取得する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return businessZoneAccountData BusinessZoneAccountデータ
     */
    function getBusinessZoneAccountData(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (BusinessZoneAccountData memory businessZoneAccountData);

    /**
     * @dev BusinessZoneAccountデータを設定する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param businessZoneAccountData BusinessZoneAccountデータ
     */
    function setBusinessZoneAccountData(
        uint16 zoneId,
        bytes32 accountId,
        BusinessZoneAccountData memory businessZoneAccountData
    ) external;

    /**
     * @dev アカウントの残高を加算する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param amount 加算する数量
     */
    function addAccountBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external;

    /**
     * @dev アカウントの残高を減算する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param amount 減算する数量
     */
    function subtractAccountBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external;

    ///////////////////////////////////
    // アカウント存在確認管理
    ///////////////////////////////////

    /**
     * @dev アカウント存在確認フラグを取得する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return exists 存在確認フラグ
     */
    function getAccountIdExistenceByZoneId(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool exists);

    /**
     * @dev アカウント存在確認フラグを設定する
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param exists 存在確認フラグ
     */
    function setAccountIdExistenceByZoneId(
        uint16 zoneId,
        bytes32 accountId,
        bool exists
    ) external;

    ///////////////////////////////////
    // バックアップ・リストア用
    ///////////////////////////////////

    /**
     * @dev 全BusinessZoneAccountデータを設定する（バックアップ・リストア用）
     * @param bizAccounts 全BusinessZoneAccountデータ
     * @param deadline 署名期限
     * @param signature 署名
     */
    function setBizAccountsAll(
        BizAccountsAll memory bizAccounts,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev 全BusinessZoneAccountデータを取得する（バックアップ・リストア用）
     * @param index インデックス
     * @return bizAccounts 全BusinessZoneAccountデータ
     */
    function getBizAccountsAll(uint256 index)
        external
        view
        returns (BizAccountsAll memory bizAccounts);
}
