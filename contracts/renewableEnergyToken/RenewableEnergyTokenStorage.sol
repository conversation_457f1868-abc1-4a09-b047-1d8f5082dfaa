// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/CountersUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/StringsUpgradeable.sol";
import "../interfaces//IContractManager.sol";
import "../interfaces/Error.sol";
import "../interfaces/RenewableEnergyTokenStruct.sol";
import "../interfaces/IRenewableEnergyToken.sol";
import "../interfaces/IRenewableEnergyTokenStorage.sol";
import "../interfaces/ITransferable.sol";
import "./libraries/StringUtils.sol";

contract RenewableEnergyTokenStorage is Initializable, IRenewableEnergyTokenStorage {
    ///////////////////////////////////
    // libraries
    ///////////////////////////////////

    using StringsUpgradeable for uint256;
    using CountersUpgradeable for CountersUpgradeable.Counter;
    using StringUtils for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev RenewableEnergyTokenLogicコントラクトアドレス */
    address private _renewableEnergyTokenLogicAddr;

    /** @dev tokenId */
    bytes32[] private _tokenIds;
    /** @dev RenewableEnergyTokenData */
    mapping(bytes32 => RenewableEnergyTokenData) private _renewableEnergyTokenData;
    /** @dev アカウントIDごとの所持トークン一覧 */
    mapping(bytes32 => bytes32[]) private _tokenIdsByAccountId;

    /* トークンの有効状態 TODO: 現時点のCoreAPIの観点からは利用しないが、将来的に利用を検討 */
    // bytes32 private _enabled;
    /* トークンの名称 TODO: 現時点のCoreAPIの観点からは利用しないが、将来的に利用を検討 */
    // bytes32 private _tokenName;
    /** @dev 発行可能総数 TODO: 現時点のCoreAPIの観点からは利用しないが、将来的に利用を検討 */
    // uint256 private totalSupply;

    /* @dev setTokenAllのsignature検証用 */
    string private constant SET_RETOKEN_ALL_SIGNATURE = "setRETokensAll";

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    /**
     * @dev TokenLogicコントラクトからのみ呼び出し可能を保証するmodifier
     */
    modifier onlyRenewableEnergyTokenLogic() {
        require(msg.sender == _renewableEnergyTokenLogicAddr, Error.RV0015_RETOKEN_INVALID_VAL);
        _;
    }

    modifier adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) {
        (bool has, string memory _err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(_err).length == 0, _err);
        require(has, Error.RV0001_ACTRL_BAD_ROLE);
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * @param renewableEnergyTokenLogicAddr TokenLogicコントラクトアドレス
     */
    function initialize(IContractManager contractManager, address renewableEnergyTokenLogicAddr)
        public
        initializer
    {
        require(
            address(contractManager) != address(0) && renewableEnergyTokenLogicAddr != address(0),
            Error.RV0015_RETOKEN_INVALID_VAL
        );
        _contractManager = contractManager;
        _renewableEnergyTokenLogicAddr = renewableEnergyTokenLogicAddr;
    }

    /**
     * @dev コントラクトバージョン取得。
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @dev トークンの詳細情報を取得する
     *
     * @param tokenId トークンID
     * @return renewableEnergyTokenData トークンデータ
     */
    function getTokenById(bytes32 tokenId)
        external
        view
        override
        onlyRenewableEnergyTokenLogic
        returns (RenewableEnergyTokenData memory)
    {
        return _renewableEnergyTokenData[tokenId];
    }

    /**
     * @dev 指定されたtokenIdの所有者と前の所有者を更新する
     * @param fromAccountId 移転元アカウントID
     * @param toAccountId 移転先アカウントID
     * @param tokenId トークンID
     */
    function setNewAccountIdForToken(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 tokenId
    ) external override {
        _renewableEnergyTokenData[tokenId].ownerAccountId = toAccountId;
        _renewableEnergyTokenData[tokenId].previousAccountId = fromAccountId;
    }

    /**
     * @dev mint関数のtokenId配列にtokenIdを追加する
     *
     * @param tokenId トークンID
     */
    function addTokenId(bytes32 tokenId) external override onlyRenewableEnergyTokenLogic {
        _tokenIds.push(tokenId);
    }

    /**
     * @dev mint関数でaccountIdにtokenIdを追加する
     * @param accountId アカウントID
     * @param tokenId トークンID
     */
    function addTokenIdToAccountId(bytes32 accountId, bytes32 tokenId)
        external
        override
        onlyRenewableEnergyTokenLogic
    {
        _tokenIdsByAccountId[accountId].push(tokenId);
    }

    /**
     * @dev トークンを追加する
     * @param tokenId トークンID
     * @param metadataId メタデータID
     * @param metadataHash メタデータハッシュ
     * @param mintAccountId 発行するアカウントID
     * @param ownerAccountId 所有者のアカウントID
     * @param isLocked トークンのロック状態
     */
    function addToken(
        bytes32 tokenId,
        bytes32 metadataId,
        bytes32 metadataHash,
        bytes32 mintAccountId,
        bytes32 ownerAccountId,
        bool isLocked
    ) external override onlyRenewableEnergyTokenLogic {
        _renewableEnergyTokenData[tokenId].tokenStatus = TokenStatus.Active;
        _renewableEnergyTokenData[tokenId].metadataId = metadataId;
        _renewableEnergyTokenData[tokenId].metadataHash = metadataHash;
        _renewableEnergyTokenData[tokenId].mintAccountId = mintAccountId;
        _renewableEnergyTokenData[tokenId].ownerAccountId = ownerAccountId;
        _renewableEnergyTokenData[tokenId].previousAccountId = mintAccountId;
        _renewableEnergyTokenData[tokenId].isLocked = isLocked;
    }

    /**
     * @dev 移転元アカウントの該当トークン所有情報を削除する
     * @param accountId 移転もとのアカウントID
     * @param tokenId キーとなるトークンID
     */
    function removeTokenIdFromAccountId(bytes32 accountId, bytes32 tokenId) external override {
        for (uint256 i = 0; i < _tokenIdsByAccountId[accountId].length; i++) {
            if (_tokenIdsByAccountId[accountId][i] == tokenId) {
                _tokenIdsByAccountId[accountId][i] = _tokenIdsByAccountId[accountId][
                    _tokenIdsByAccountId[accountId].length - 1
                ];
                _tokenIdsByAccountId[accountId].pop();
                break;
            }
        }
    }

    /**
     * @dev 指定されたtokenIdに紐づくToken情報を登録、もしくは上書きする
     * @param token Tokenの情報
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setRenewableEnergyTokenAll(
        RenewableEnergyTokenAll memory token,
        uint256 deadline,
        bytes memory signature
    )
        external
        override
        // Admin権限を持つかチェック
        adminOnly(keccak256(abi.encode(SET_RETOKEN_ALL_SIGNATURE, deadline)), deadline, signature)
    {
        bytes32 tokenId = token.tokenId;
        _tokenIds.push(tokenId);
        _renewableEnergyTokenData[tokenId] = token.renewableEnergyTokenData;
    }

    /**
     * @dev 指定されたaccountIdに紐づくTokenIdを登録、もしくは上書きする
     * @param tokenIdByAccountId accountIdに紐づくTokenId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setTokenIdsByAccountIdAll(
        TokenIdsByAccountIdAll memory tokenIdByAccountId,
        uint256 deadline,
        bytes memory signature
    )
        external
        override
        // Admin権限を持つかチェック
        adminOnly(keccak256(abi.encode(SET_RETOKEN_ALL_SIGNATURE, deadline)), deadline, signature)
    {
        bytes32 accountId = tokenIdByAccountId.accountId;
        _tokenIdsByAccountId[accountId] = tokenIdByAccountId.tokenIds;
    }

    /**
     * @dev トークンの詳細情報を取得する
     *
     * @param tokenId トークンID
     * @return renewableEnergyTokenData トークンデータ
     * @return err エラーメッセージ
     */
    function getToken(bytes32 tokenId)
        external
        view
        override
        returns (RenewableEnergyTokenData memory renewableEnergyTokenData, string memory err)
    {
        return (_renewableEnergyTokenData[tokenId], err);
    }

    /**
     * @dev Tokenの数を返却する。
     *
     * @return count tokenの数
     */
    function getTokenCount() external view override returns (uint256 count) {
        return _tokenIds.length;
    }

    /**
     * @dev 指定されたインデックスのRenewableEnergyTokenAll情報を取得する
     * @param index 取得tokensのindex
     */
    function getRenewableEnergyTokenAll(uint256 index)
        external
        view
        override
        returns (RenewableEnergyTokenAll memory renewableEnergyToken)
    {
        bytes32 tokenId = _tokenIds[index];
        renewableEnergyToken.tokenId = tokenId;
        renewableEnergyToken.renewableEnergyTokenData = _renewableEnergyTokenData[tokenId];

        return renewableEnergyToken;
    }

    /**
     * @dev 指定されたインデックスに対応するToken IDを返却する。
     *
     * @param index 取得したいTokenのインデックス
     * @return tokenId 対応するTokenのID
     */
    function getTokenIdsByIndex(uint256 index) external view override returns (bytes32) {
        return _tokenIds[index];
    }

    /**
     * @dev 指定されたaccountIdに紐づくTokenIdを取得
     * @param accountId accountId
     * @return tokenIdsByAccountId accountIdに紐づくTokenIdのリスト
     */
    function getTokenIdsByAccountIdAll(bytes32 accountId)
        external
        view
        override
        returns (TokenIdsByAccountIdAll memory tokenIdsByAccountId)
    {
        tokenIdsByAccountId.accountId = accountId;
        tokenIdsByAccountId.tokenIds = _tokenIdsByAccountId[accountId];

        return (tokenIdsByAccountId);
    }

    /**
     * @dev RenewableEnergyTokenLogicアドレスを更新する（Admin権限必要）
     * @param renewableEnergyTokenLogicAddr 新しいRenewableEnergyTokenLogicアドレス
     */
    function setRenewableEnergyTokenLogicAddress(address renewableEnergyTokenLogicAddr)
        external
        override
    {
        // Admin権限チェック
        require(renewableEnergyTokenLogicAddr != address(0), Error.RV0015_RETOKEN_INVALID_VAL);
        _renewableEnergyTokenLogicAddr = renewableEnergyTokenLogicAddr;
    }

    /**
     * @dev RenewableEnergyTokenLogicアドレスを取得する
     * @return renewableEnergyTokenLogicAddr AccountLogicアドレス
     */
    function getRenewableEnergyTokenLogicAddress() external view override returns (address) {
        return _renewableEnergyTokenLogicAddr;
    }

    /**
     * @dev ContractManagerアドレスを更新する（将来の拡張用）
     * @param contractManagerAddr 新しいContractManagerアドレス
     */
    function setContractManagerAddress(address contractManagerAddr) external override {
        require(contractManagerAddr != address(0), Error.RV0015_RETOKEN_INVALID_VAL);
        _contractManager = IContractManager(contractManagerAddr);
    }

    /**
     * @dev ContractManagerアドレスを取得する
     * @return contractManagerAddr ContractManagerアドレス
     */
    function getContractManagerAddress() external view override returns (address) {
        return address(_contractManager);
    }
}
