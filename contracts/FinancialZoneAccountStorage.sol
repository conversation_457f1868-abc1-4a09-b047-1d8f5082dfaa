// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IFinancialZoneAccountStorage.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/Struct.sol";
import "./interfaces/Error.sol";
import "./remigration/RemigrationLib.sol";

contract FinancialZoneAccountStorage is Initializable, IFinancialZoneAccountStorage {
    using RemigrationLib for *;
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev FinancialZoneAccountLogicコントラクトアドレス */
    address private _financialZoneAccountLogicAddr;

    /** @dev アカウント限度額情報(accountId => AccountData) */
    mapping(bytes32 => FinancialZoneAccountData) private _financialAccountData;

    /** @dev 1日あたりの秒数 */
    uint256 private constant _DAILY_TIME = 86400;
    /** @dev UTCとJSTの時差 */
    uint256 private constant _JST_TIME_DIFFERENCE = 32400;

    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant _EMPTY_LENGTH = 0;
    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant _GET_FINACCOUNTS_LIMIT = 1000;
    /** @dev 各種限度額のチェック対象(mint) **/
    bytes32 private constant _CHECK_MINT = "checkMint";
    /** @dev 各種限度額のチェック対象(burn) **/
    bytes32 private constant _CHECK_BURN = "checkBurn";
    /** @dev 各種限度額のチェック対象(charge) **/
    bytes32 private constant _CHECK_CHARGE = "checkCharge";
    /** @dev 各種限度額のチェック対象(discharge) **/
    bytes32 private constant _CHECK_DISCHARGE = "checkDischarge";
    /** @dev 各種限度額のチェック対象(transfer) **/
    bytes32 private constant _CHECK_TRANSFER = "checkTransfer";
    /** @dev getFinAccountsAllのsignature検証用 **/
    string private constant _GET_FINACCOUNTS_ALL_SIGNATURE = "getFinAccountsAll";
    /* @dev setBizAccountsAllのsignature検証用 */
    string private constant _SET_FINACCOUNTS_ALL_SIGNATURE = "setFinAccountsAll";

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    /**
     * @dev FinancialZoneAccountLogicコントラクトからのみ呼び出し可能を保証するmodifier
     */
    modifier financialZoneAccountOnly() {
        require(msg.sender == _financialZoneAccountLogicAddr, Error.GA0030_INVALID_CALLER_ADDRESS);
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * @param financialZoneAccountLogicAddr FinancialZoneAccountLogicコントラクトアドレス
     */
    function initialize(IContractManager contractManager, address financialZoneAccountLogicAddr)
        public
        initializer
    {
        require(
            address(contractManager) != address(0),
            Error.RV0009_FINANCIAL_ZONE_ACCOUNT_INVALID_VAL
        );
        require(
            financialZoneAccountLogicAddr != address(0),
            Error.RV0009_FINANCIAL_ZONE_ACCOUNT_INVALID_VAL
        );

        _contractManager = contractManager;
        _financialZoneAccountLogicAddr = financialZoneAccountLogicAddr;
    }

    /**
     * @dev コントラクトバージョン取得。
     * @return version コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev 発行者データを取得する
     * @param accountId マッピングのキーとなるアカウントID
     * @return financialZoneAccountData 発行者データ
     */
    function getFinancialZoneAccountData(bytes32 accountId)
        external
        view
        financialZoneAccountOnly
        returns (FinancialZoneAccountData memory financialZoneAccountData)
    {
        return _financialAccountData[accountId];
    }

    /**
     * @dev 発行者データを設定する
     * @param accountId マッピングのキーとなるアカウントID
     * @param financialZoneAccountData 発行者データ
     */
    function setFinancialZoneAccountData(
        bytes32 accountId,
        FinancialZoneAccountData memory financialZoneAccountData
    ) external financialZoneAccountOnly {
        _financialAccountData[accountId] = financialZoneAccountData;
    }

    /**
     * @dev 発行者データを取得する
     * @param accountId マッピングのキーとなるアカウントID
     * @return cumulativeTransactionLimits 発行者データ
     */
    function getCumulativeTransactionLimits(bytes32 accountId)
        external
        view
        financialZoneAccountOnly
        returns (CumulativeTransactionLimits memory cumulativeTransactionLimits)
    {
        return _financialAccountData[accountId].cumulativeTransactionLimits;
    }

    /**
     * @dev 発行者データを設定する
     * @param accountId マッピングのキーとなるアカウントID
     * @param cumulativeTransactionLimits 発行者データ
     */
    function setCumulativeTransactionLimits(
        bytes32 accountId,
        CumulativeTransactionLimits memory cumulativeTransactionLimits
    ) external financialZoneAccountOnly {
        _financialAccountData[accountId].cumulativeTransactionLimits = cumulativeTransactionLimits;
    }

    /**
     * @dev アカウント限度額登録
     * @param accountId マッピングのキーとなるアカウントID
     * @param limitValues アカウントの限度額値
     */
    function addAccountLimitData(bytes32 accountId, AccountLimitValues memory limitValues)
        external
        financialZoneAccountOnly
    {
        _financialAccountData[accountId].mintLimit = limitValues.mint;
        _financialAccountData[accountId].burnLimit = limitValues.burn;
        _financialAccountData[accountId].chargeLimit = limitValues.charge;
        _financialAccountData[accountId].dischargeLimit = limitValues.discharge;
        _financialAccountData[accountId].transferLimit = limitValues.transfer;
        _financialAccountData[accountId].cumulativeLimit = limitValues.cumulative.total;
        _financialAccountData[accountId]
            .cumulativeTransactionLimits
            .cumulativeMintLimit = limitValues.cumulative.mint;
        _financialAccountData[accountId]
            .cumulativeTransactionLimits
            .cumulativeBurnLimit = limitValues.cumulative.burn;
        _financialAccountData[accountId]
            .cumulativeTransactionLimits
            .cumulativeChargeLimit = limitValues.cumulative.charge;
        _financialAccountData[accountId]
            .cumulativeTransactionLimits
            .cumulativeDischargeLimit = limitValues.cumulative.discharge;
        _financialAccountData[accountId]
            .cumulativeTransactionLimits
            .cumulativeTransferLimit = limitValues.cumulative.transfer;
    }

    /**
     * @dev cumulative amount初期化
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param jstDay 現在の日付
     */
    function resetCumulative(bytes32 accountId, uint256 jstDay) external financialZoneAccountOnly {
        _financialAccountData[accountId].cumulativeAmount = Constant.RESET_CUMULATIVE_AMOUNT;
        _financialAccountData[accountId].cumulativeTransactionLimits.cumulativeMintAmount = Constant
            .RESET_CUMULATIVE_AMOUNT;
        _financialAccountData[accountId].cumulativeTransactionLimits.cumulativeBurnAmount = Constant
            .RESET_CUMULATIVE_AMOUNT;
        _financialAccountData[accountId]
            .cumulativeTransactionLimits
            .cumulativeChargeAmount = Constant.RESET_CUMULATIVE_AMOUNT;
        _financialAccountData[accountId]
            .cumulativeTransactionLimits
            .cumulativeDischargeAmount = Constant.RESET_CUMULATIVE_AMOUNT;
        _financialAccountData[accountId]
            .cumulativeTransactionLimits
            .cumulativeTransferAmount = Constant.RESET_CUMULATIVE_AMOUNT;
        _financialAccountData[accountId].cumulativeDate = jstDay;
    }

    /**
     * @dev cumulativeDate
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeDate(bytes32 accountId)
        external
        view
        financialZoneAccountOnly
        returns (uint256 cumulativeDate)
    {
        return _financialAccountData[accountId].cumulativeDate;
    }

    /**
     * @dev cumulativeAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeAmount(bytes32 accountId)
        external
        view
        financialZoneAccountOnly
        returns (uint256 cumulativeAmount)
    {
        return _financialAccountData[accountId].cumulativeAmount;
    }

    /**
     * @dev cumulativeMintAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeMintAmount(bytes32 accountId)
        external
        view
        financialZoneAccountOnly
        returns (uint256 cumulativeMintAmount)
    {
        return _financialAccountData[accountId].cumulativeTransactionLimits.cumulativeMintAmount;
    }

    /**
     * @dev cumulativeBurnAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeBurnAmount(bytes32 accountId)
        external
        view
        financialZoneAccountOnly
        returns (uint256 cumulativeBurnAmount)
    {
        return _financialAccountData[accountId].cumulativeTransactionLimits.cumulativeBurnAmount;
    }

    /**
     * @dev cumulativeChargeAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeChargeAmount(bytes32 accountId)
        external
        view
        financialZoneAccountOnly
        returns (uint256 cumulativeChargeAmount)
    {
        return _financialAccountData[accountId].cumulativeTransactionLimits.cumulativeChargeAmount;
    }

    /**
     * @dev cumulativeDischargeAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeDischargeAmount(bytes32 accountId)
        external
        view
        financialZoneAccountOnly
        returns (uint256 cumulativeAmount)
    {
        return
            _financialAccountData[accountId].cumulativeTransactionLimits.cumulativeDischargeAmount;
    }

    /**
     * @dev cumulativeTransferAmount
     * @param accountId マッピングのキーとなるアカウントID
     */
    function getCumulativeTransferAmount(bytes32 accountId)
        external
        view
        financialZoneAccountOnly
        returns (uint256 cumulativeTransferAmount)
    {
        return
            _financialAccountData[accountId].cumulativeTransactionLimits.cumulativeTransferAmount;
    }

    /**
     * @dev 指定されたAcountIdsに紐づくFinancialZoneAccounts情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     * @param finAccount finAccountsInfo
     */
    function setFinAccountAll(FinancialZoneAccountsAll memory finAccount) external {
        RemigrationLib.setFinAccountAll(_financialAccountData, finAccount);
    }

    /**
     * @dev limitとoffsetで指定したFinancialZoneAccountsを一括取得する
     * @param index index
     */
    function getFinAccountAll(uint256 index)
        external
        view
        returns (FinancialZoneAccountsAll memory finAccounts)
    {
        return
            RemigrationLib.getFinAccountAll(
                _financialAccountData,
                address(_contractManager),
                index
            );
    }

    /**
     * @dev cumulative amount初期化
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param jstDay 現在の日付
     */
    function syncCumulativeReset(bytes32 accountId, uint256 jstDay)
        external
        financialZoneAccountOnly
    {
        _financialAccountData[accountId].cumulativeAmount = 0;
        _financialAccountData[accountId].cumulativeDate = jstDay;
    }

    /**
     * @dev ContractManagerアドレスを更新する（Admin権限必要）
     * @param contractManagerAddr 新しいContractManagerアドレス
     */
    function setContractManagerAddress(address contractManagerAddr) external {
        require(contractManagerAddr != address(0), Error.RV0009_FINANCIAL_ZONE_ACCOUNT_INVALID_VAL);
        _contractManager = IContractManager(contractManagerAddr);
    }

    /**
     * @dev ContractManagerアドレスを取得する
     * @return contractManagerAddr ContractManagerアドレス
     */
    function getContractManagerAddress() external view override returns (address) {
        return address(_contractManager);
    }

    /**
     * @dev FinancialZoneAccountLogicアドレスを更新する（Admin権限必要）
     * @param financialZoneAccountLogicAddr 新しいIssuerLogicアドレス
     */
    function setFinancialZoneAccountLogicAddress(address financialZoneAccountLogicAddr) external {
        require(
            financialZoneAccountLogicAddr != address(0),
            Error.RV0009_FINANCIAL_ZONE_ACCOUNT_INVALID_VAL
        );
        _financialZoneAccountLogicAddr = financialZoneAccountLogicAddr;
    }

    /**
     * @dev FinancialZoneAccountLogicアドレスを更新する（Admin権限必要）
     * @return financialZoneAccountLogicAddr FinancialZoneAccountLogicアドレス
     */
    function getFinancialZoneAccountLogicAddress() external view returns (address) {
        return _financialZoneAccountLogicAddr;
    }
}
